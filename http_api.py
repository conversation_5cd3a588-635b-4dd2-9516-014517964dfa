#!/usr/bin/env python3
"""
光电对抗仿真系统HTTP API接口
基于FastAPI框架，封装run_simulation_api函数为HTTP接口
"""

import json
import logging
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 导入原始API函数
from api import run_simulation_api

# 创建FastAPI应用
app = FastAPI(
    title="光电对抗仿真系统API",
    description="光电对抗仿真系统HTTP接口",
    version="beta2507281138"
)

# 添加CORS中间件，允许跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)

# 定义请求模型
class SimulationRequest(BaseModel):
    """仿真请求模型"""
    simulation: Dict[str, Any]
    system: Dict[str, Any]
    optical_targets: list
    optical_jammers: Optional[list] = None
    optical_recons: Optional[list] = None
    output_base_dir: Optional[str] = None
    log_level: Optional[str] = "INFO"
    num_threads: Optional[int] = None

# 定义响应模型
class SimulationResponse(BaseModel):
    """仿真响应模型"""
    success: bool
    session_info: Dict[str, Any]
    simulation_config: Dict[str, Any]
    output_structure: Dict[str, Any]
    simulation_results: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    error_info: Optional[str] = None

@app.post("/run_simulation", response_model=SimulationResponse)
async def run_simulation(request: SimulationRequest):
    """
    运行光电对抗仿真
    
    Args:
        request: 仿真请求参数
        
    Returns:
        仿真结果
    """
    try:
        # 构建配置字典
        config_dict = {
            "simulation": request.simulation,
            "system": request.system,
            "optical_targets": request.optical_targets
        }
        
        # 添加可选字段
        if request.optical_jammers is not None:
            config_dict["optical_jammers"] = request.optical_jammers
        if request.optical_recons is not None:
            config_dict["optical_recons"] = request.optical_recons
        
        # 调用原始API函数
        result_json = run_simulation_api(
            config_input=config_dict,
            output_base_dir=request.output_base_dir,
            log_level=request.log_level,
            num_threads=request.num_threads
        )
        
        # 解析JSON结果
        result_dict = json.loads(result_json)
        
        return SimulationResponse(**result_dict)
        
    except Exception as e:
        # 记录错误
        logging.error(f"仿真执行失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"仿真执行失败: {str(e)}")

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "光电对抗仿真系统HTTP API",
        "version": "1.0.0",
        "endpoints": {
            "POST /run_simulation": "运行光电对抗仿真",
            "GET /": "API信息"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "message": "API服务正常运行"}

if __name__ == "__main__":
    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",  # 监听所有地址
        port=8587,       # 指定端口
        log_level="info"
    )
