"""
输出管理模块
负责管理仿真结果的输出，包括目录创建、文件组织、批次隔离等
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import shutil


logger = logging.getLogger(__name__)


class OutputManager:
    """输出管理器"""
    
    def __init__(self, base_dir: Optional[str] = None):
        """
        初始化输出管理器
        
        Args:
            base_dir: 基础输出目录，如果为None则使用默认目录
        """
        if base_dir is None:
            self.base_dir = Path("./simulation_results")
        else:
            self.base_dir = Path(base_dir)
        
        self.session_dir = None
        self.session_id = None
        
        # 创建基础目录
        self.base_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"输出管理器初始化，基础目录: {self.base_dir}")
    
    def create_session_dir(self) -> str:
        """
        创建会话目录，用于隔离不同批次的调用
        
        Returns:
            会话目录路径
        """
        # 生成会话ID（基于时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精确到毫秒
        self.session_id = f"session_{timestamp}"
        
        # 创建会话目录
        self.session_dir = self.base_dir / self.session_id
        self.session_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录结构
        subdirs = [
            "images",      # 图像文件
            "videos",      # 视频文件
            "data",        # 数据文件
            "logs",        # 日志文件
            "configs"      # 配置文件
        ]
        
        for subdir in subdirs:
            (self.session_dir / subdir).mkdir(exist_ok=True)
        
        logger.info(f"创建会话目录: {self.session_dir}")
        return str(self.session_dir)
    
    def get_output_path(self, category: str, filename: str) -> str:
        """
        获取输出文件路径
        
        Args:
            category: 文件类别 (images, videos, data, logs, configs)
            filename: 文件名
            
        Returns:
            完整文件路径
        """
        if self.session_dir is None:
            raise RuntimeError("会话目录未创建，请先调用 create_session_dir()")
        
        category_dir = self.session_dir / category
        category_dir.mkdir(exist_ok=True)
        
        return str(category_dir / filename)
    
    def save_json_data(self, data: Any, category: str, filename: str) -> str:
        """
        保存JSON数据
        
        Args:
            data: 要保存的数据
            category: 文件类别
            filename: 文件名（不包含扩展名）
            
        Returns:
            保存的文件路径
        """
        if not filename.endswith('.json'):
            filename += '.json'
        
        file_path = self.get_output_path(category, filename)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"JSON数据已保存: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"保存JSON数据失败: {e}")
            raise
    
    def save_csv_data(self, data: List[Dict], category: str, filename: str) -> str:
        """
        保存CSV数据
        
        Args:
            data: 要保存的数据列表
            category: 文件类别
            filename: 文件名（不包含扩展名）
            
        Returns:
            保存的文件路径
        """
        import csv
        
        if not filename.endswith('.csv'):
            filename += '.csv'
        
        file_path = self.get_output_path(category, filename)
        
        try:
            if not data:
                logger.warning(f"数据为空，跳过保存: {filename}")
                return file_path
            
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
            
            logger.debug(f"CSV数据已保存: {file_path} ({len(data)} 条记录)")
            return file_path
            
        except Exception as e:
            logger.error(f"保存CSV数据失败: {e}")
            raise
    
    def save_image(self, image_array, category: str, filename: str, format: str = 'PNG') -> str:
        """
        保存图像文件
        
        Args:
            image_array: 图像数组 (numpy array)
            category: 文件类别
            filename: 文件名（不包含扩展名）
            format: 图像格式
            
        Returns:
            保存的文件路径
        """
        from PIL import Image
        import numpy as np
        
        if not filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            filename += f'.{format.lower()}'
        
        file_path = self.get_output_path(category, filename)
        
        try:
            # 确保图像数据格式正确
            if isinstance(image_array, np.ndarray):
                if image_array.dtype != np.uint8:
                    image_array = (image_array * 255).astype(np.uint8)
                
                image = Image.fromarray(image_array)
            else:
                image = image_array
            
            image.save(file_path, format=format)
            logger.debug(f"图像已保存: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"保存图像失败: {e}")
            raise
    
    def create_summary_report(self, results: Dict[str, Any]) -> str:
        """
        创建仿真结果摘要报告
        
        Args:
            results: 仿真结果数据
            
        Returns:
            报告文件路径
        """
        if self.session_dir is None:
            raise RuntimeError("会话目录未创建")
        
        report_data = {
            "session_info": {
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat(),
                "output_directory": str(self.session_dir)
            },
            "results_summary": results,
            "file_statistics": self._get_file_statistics()
        }
        
        report_path = self.save_json_data(report_data, "data", "simulation_summary")
        logger.info(f"仿真摘要报告已生成: {report_path}")
        return report_path
    
    def _get_file_statistics(self) -> Dict[str, Any]:
        """获取文件统计信息"""
        if self.session_dir is None:
            return {}
        
        stats = {}
        
        for category in ["images", "videos", "data", "logs", "configs"]:
            category_dir = self.session_dir / category
            if category_dir.exists():
                files = list(category_dir.glob("*"))
                stats[category] = {
                    "count": len(files),
                    "total_size": sum(f.stat().st_size for f in files if f.is_file()),
                    "files": [f.name for f in files if f.is_file()]
                }
            else:
                stats[category] = {"count": 0, "total_size": 0, "files": []}
        
        return stats
    
    def cleanup_old_sessions(self, keep_days: int = 7):
        """
        清理旧的会话目录
        
        Args:
            keep_days: 保留天数
        """
        try:
            cutoff_time = datetime.now().timestamp() - (keep_days * 24 * 3600)
            
            for session_dir in self.base_dir.glob("session_*"):
                if session_dir.is_dir():
                    if session_dir.stat().st_mtime < cutoff_time:
                        shutil.rmtree(session_dir)
                        logger.info(f"已清理旧会话目录: {session_dir}")
                        
        except Exception as e:
            logger.warning(f"清理旧会话目录失败: {e}")
    
    def get_session_info(self) -> Dict[str, str]:
        """获取当前会话信息"""
        return {
            "session_id": self.session_id or "未创建",
            "session_dir": str(self.session_dir) if self.session_dir else "未创建",
            "base_dir": str(self.base_dir)
        }
