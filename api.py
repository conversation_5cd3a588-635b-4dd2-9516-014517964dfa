#!/usr/bin/env python3
"""
光电对抗仿真系统API入口
提供函数式接口，传入JSON配置字典或JSON字符串，返回JSON字符串格式的完整输出信息
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
import traceback

# 导入核心模块
from PhoElec.core.config_manager import ConfigManager
from PhoElec.core.output_manager import OutputManager
from PhoElec.core.simulation_engine import SimulationEngine
from PhoElec.utils.logger import setup_logger


def run_simulation_api(
    config_input: Union[Dict[str, Any], str],
    output_base_dir: Optional[str] = None,
    log_level: str = 'INFO',
    num_threads: Optional[int] = None
) -> str:
    """
    光电对抗仿真系统API函数

    Args:
        config_input: 仿真配置，可以是字典类型或JSON字符串，包含simulation、system、optical_targets等配置
                     可选字段（如optical_jammers、optical_recons等）如果不存在不会报错
        output_base_dir: 输出基础目录路径，如果为None则使用默认目录
        log_level: 日志级别，可选值: DEBUG, INFO, WARNING, ERROR
        num_threads: 并行线程数，如果为None则自动检测

    Returns:
        JSON字符串格式的结果，包含以下信息的字典:
        {
            "success": bool,                    # 仿真是否成功
            "session_info": {                   # 会话信息
                "session_id": str,              # 会话ID
                "start_time": str,              # 开始时间
                "end_time": str,                # 结束时间
                "duration": float,              # 执行时长(秒)
                "output_directory": str         # 输出目录完整路径
            },
            "simulation_config": {              # 仿真配置摘要
                "scenario_name": str,           # 场景名称
                "duration": float,              # 仿真时长
                "data_count": int,              # 数据生成数量
                "output_types": List[str],      # 输出类型
                "device_count": Dict[str, int]  # 各类设备数量
            },
            "output_structure": {               # 输出文件夹完整结构
                "total_files": int,             # 文件总数
                "total_size": int,              # 总大小(字节)
                "categories": {                 # 各类别文件信息
                    "images": {
                        "count": int,
                        "size": int,
                        "files": List[str]
                    },
                    "videos": {
                        "count": int,
                        "size": int,
                        "files": List[str]
                    },
                    "data": {
                        "count": int,
                        "size": int,
                        "files": List[str]
                    },
                    "logs": {
                        "count": int,
                        "size": int,
                        "files": List[str]
                    },
                    "configs": {
                        "count": int,
                        "size": int,
                        "files": List[str]
                    }
                }
            },
            "simulation_results": {             # 仿真结果文件路径
                "images": List[str],            # 图像文件路径列表
                "videos": List[str],            # 视频文件路径列表
                "data": List[str],              # 数据文件路径列表
                "summary": List[str]            # 摘要文件路径列表
            },
            "performance_metrics": {            # 性能指标
                "processing_speed": float,      # 处理速度(数据/秒)
                "memory_usage": Dict[str, Any], # 内存使用情况
                "thread_utilization": float    # 线程利用率
            },
            "error_info": Optional[str]         # 错误信息(如果失败)
        }
        
    Raises:
        ValueError: 配置参数无效
        RuntimeError: 仿真执行失败
    """
    
    # 记录开始时间
    start_time = datetime.now()
    
    # 初始化返回结果
    result = {
        "success": False,
        "session_info": {},
        "simulation_config": {},
        "output_structure": {},
        "simulation_results": {},
        "performance_metrics": {},
        "error_info": None
    }
    
    logger = None
    output_manager = None
    
    try:
        # 1. 解析和验证配置参数
        config_dict = _parse_config_input(config_input)
        
        # 2. 初始化输出管理器
        output_manager = OutputManager(base_dir=output_base_dir)
        output_dir = output_manager.create_session_dir()
        
        # 3. 设置日志系统
        log_file_path = os.path.join(output_dir, 'logs', 'simulation.log')
        logger = setup_logger(
            name='simulation_api',
            log_level=log_level,
            log_file=log_file_path,
            console_output=False  # API模式下不输出到控制台
        )
        
        logger.info("="*80)
        logger.info("光电对抗仿真系统API启动")
        logger.info("="*80)
        logger.info(f"会话ID: {output_manager.session_id}")
        logger.info(f"输出目录: {output_dir}")
        logger.info(f"日志级别: {log_level}")
        
        # 4. 保存配置文件到输出目录
        config_backup_path = output_manager.save_json_data(
            config_dict, 'configs', 'input_config'
        )
        logger.info(f"输入配置已保存: {config_backup_path}")
        
        # 5. 初始化配置管理器并验证
        logger.info("初始化配置管理器...")
        config_manager = ConfigManager(config_dict)
        config_manager.validate()
        logger.info("配置验证通过")
        
        # 6. 初始化仿真引擎
        logger.info("初始化仿真引擎...")
        simulation_engine = SimulationEngine(
            config_manager=config_manager,
            output_manager=output_manager,
            num_threads=num_threads
        )
        
        # 7. 执行仿真
        logger.info("开始执行仿真...")
        simulation_results = simulation_engine.run()
        
        # 8. 记录结束时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 9. 构建返回结果
        result.update({
            "success": True,
            "session_info": _build_session_info(
                output_manager, start_time, end_time, duration
            ),
            "simulation_config": _build_simulation_config(config_manager),
            "output_structure": _build_output_structure(output_manager),
            "simulation_results": simulation_results,
            "performance_metrics": _build_performance_metrics(
                config_manager, duration, simulation_results
            )
        })
        
        logger.info("="*80)
        logger.info("仿真执行成功完成")
        logger.info("="*80)
        logger.info(f"执行时间: {duration:.2f} 秒")
        logger.info(f"输出目录: {output_dir}")
        logger.info(f"生成文件总数: {result['output_structure']['total_files']}")

        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        # 记录错误信息
        error_msg = f"仿真执行失败: {str(e)}"
        error_detail = traceback.format_exc()
        
        if logger:
            logger.error(error_msg)
            logger.error(f"详细错误信息:\n{error_detail}")
        
        # 更新结果
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        result.update({
            "success": False,
            "error_info": error_msg,
            "session_info": _build_session_info(
                output_manager, start_time, end_time, duration
            ) if output_manager else {}
        })
        
        # 如果有输出管理器，尝试保存错误信息
        if output_manager and output_manager.session_dir:
            try:
                error_data = {
                    "error_message": error_msg,
                    "error_detail": error_detail,
                    "timestamp": datetime.now().isoformat(),
                    "config_dict": config_dict
                }
                output_manager.save_json_data(error_data, 'logs', 'error_report')
            except:
                pass  # 忽略保存错误信息时的异常

        return json.dumps(result, ensure_ascii=False, indent=2)


def _parse_config_input(config_input: Union[Dict[str, Any], str]) -> Dict[str, Any]:
    """
    解析配置输入，支持字典和JSON字符串

    Args:
        config_input: 配置输入，可以是字典或JSON字符串

    Returns:
        解析后的配置字典

    Raises:
        ValueError: 配置参数无效
    """
    if isinstance(config_input, dict):
        config_dict = config_input
    elif isinstance(config_input, str):
        try:
            config_dict = json.loads(config_input)
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON字符串解析失败: {str(e)}")
    else:
        raise ValueError("配置参数必须是字典类型或JSON字符串")

    if not config_dict:
        raise ValueError("配置参数不能为空")

    if not isinstance(config_dict, dict):
        raise ValueError("解析后的配置必须是字典类型")

    # 确保可选的optical字段存在，即使为空列表
    optional_optical_fields = [
        'optical_targets', 'optical_jammers', 'optical_recons'
    ]

    for field in optional_optical_fields:
        if field not in config_dict:
            config_dict[field] = []

    return config_dict


def _build_session_info(
    output_manager: OutputManager,
    start_time: datetime,
    end_time: datetime,
    duration: float
) -> Dict[str, Any]:
    """构建会话信息"""
    session_info = {
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat(),
        "duration": duration
    }
    
    if output_manager:
        session_info.update({
            "session_id": output_manager.session_id or "未创建",
            "output_directory": str(output_manager.session_dir) if output_manager.session_dir else "未创建"
        })
    
    return session_info


def _build_simulation_config(config_manager: ConfigManager) -> Dict[str, Any]:
    """构建仿真配置摘要"""
    return {
        "scenario_name": config_manager.simulation.scenario_name,
        "duration": config_manager.simulation.duration,
        "time_step": config_manager.simulation.time_step,
        "data_count": config_manager.simulation.data_count,
        "output_types": config_manager.simulation.output_types.copy(),
        "device_count": config_manager.get_device_count(),
        "environment": config_manager.simulation.environment.copy(),
        "system_config": {
            "max_threads": config_manager.system.max_threads,
            "image_resolution": config_manager.system.image_resolution,
            "video_fps": config_manager.system.video_fps,
            "random_seed": config_manager.system.random_seed
        }
    }


def _build_output_structure(output_manager: OutputManager) -> Dict[str, Any]:
    """构建输出文件夹结构信息"""
    if not output_manager or not output_manager.session_dir:
        return {
            "total_files": 0,
            "total_size": 0,
            "categories": {}
        }

    # 获取文件统计信息
    file_stats = output_manager._get_file_statistics()

    # 计算总数和总大小
    total_files = sum(stats["count"] for stats in file_stats.values())
    total_size = sum(stats["total_size"] for stats in file_stats.values())

    # 构建详细的文件结构信息
    categories = {}
    for category, stats in file_stats.items():
        categories[category] = {
            "count": stats["count"],
            "size": stats["total_size"],
            "files": []
        }

        # 获取每个文件的详细信息
        category_dir = output_manager.session_dir / category
        if category_dir.exists():
            for file_path in category_dir.glob("*"):
                if file_path.is_file():
                    categories[category]["files"].append({
                        "name": file_path.name,
                        "path": str(file_path),
                        "size": file_path.stat().st_size,
                        "modified_time": datetime.fromtimestamp(
                            file_path.stat().st_mtime
                        ).isoformat()
                    })

    return {
        "total_files": total_files,
        "total_size": total_size,
        "categories": categories,
        "directory_tree": _build_directory_tree(output_manager.session_dir)
    }


def _build_directory_tree(root_dir: Path) -> Dict[str, Any]:
    """构建目录树结构"""
    if not root_dir.exists():
        return {}

    tree = {
        "name": root_dir.name,
        "path": str(root_dir),
        "type": "directory",
        "children": []
    }

    try:
        for item in sorted(root_dir.iterdir()):
            if item.is_dir():
                # 递归处理子目录
                subtree = _build_directory_tree(item)
                tree["children"].append(subtree)
            else:
                # 处理文件
                file_info = {
                    "name": item.name,
                    "path": str(item),
                    "type": "file",
                    "size": item.stat().st_size,
                    "modified_time": datetime.fromtimestamp(
                        item.stat().st_mtime
                    ).isoformat()
                }
                tree["children"].append(file_info)
    except PermissionError:
        # 处理权限错误
        tree["error"] = "权限不足"

    return tree


def _build_performance_metrics(
    config_manager: ConfigManager,
    duration: float,
    simulation_results: Dict[str, List[str]]
) -> Dict[str, Any]:
    """构建性能指标"""
    # 计算处理速度
    total_data_count = config_manager.simulation.data_count
    processing_speed = total_data_count / duration if duration > 0 else 0

    # 计算文件生成统计
    total_files = sum(len(files) for files in simulation_results.values())

    # 获取内存使用情况（简化版）
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_usage = {
            "rss": memory_info.rss,  # 物理内存使用
            "vms": memory_info.vms,  # 虚拟内存使用
            "percent": process.memory_percent()  # 内存使用百分比
        }
    except ImportError:
        # 如果psutil不可用，使用简化的内存信息
        memory_usage = {
            "rss": 0,
            "vms": 0,
            "percent": 0.0
        }

    # 计算线程利用率（简化计算）
    max_threads = config_manager.system.max_threads
    # 假设在仿真期间线程利用率为80%（实际应用中可以通过监控获得更准确的数据）
    thread_utilization = 0.8 if max_threads > 1 else 1.0

    return {
        "processing_speed": processing_speed,
        "files_per_second": total_files / duration if duration > 0 else 0,
        "thread_count": max_threads,
        "thread_utilization": thread_utilization,
        "memory_usage": memory_usage,
        "execution_efficiency": {
            "data_per_second": processing_speed,
            "time_per_data": duration / total_data_count if total_data_count > 0 else 0,
            "total_operations": total_data_count,
            "successful_operations": total_files
        }
    }


def validate_config_dict(config_input: Union[Dict[str, Any], str]) -> Tuple[bool, List[str]]:
    """
    验证配置的基本结构和必要字段

    Args:
        config_input: 配置输入，可以是字典或JSON字符串

    Returns:
        (是否有效, 错误信息列表)
    """
    errors = []

    try:
        config_dict = _parse_config_input(config_input)
    except ValueError as e:
        return False, [str(e)]

    # 检查必要的顶级字段
    required_sections = ['simulation']
    for section in required_sections:
        if section not in config_dict:
            errors.append(f"缺少必要配置节: {section}")

    # 检查simulation配置
    if 'simulation' in config_dict:
        sim_config = config_dict['simulation']
        required_sim_fields = ['scenario_name', 'duration', 'data_count']
        for field in required_sim_fields:
            if field not in sim_config:
                errors.append(f"simulation配置缺少必要字段: {field}")

    # 检查是否至少有一种设备配置
    device_sections = ['optical_targets', 'optical_jammers', 'optical_recons']
    has_device = any(section in config_dict and config_dict[section] for section in device_sections)
    if not has_device:
        errors.append("至少需要配置一种设备 (optical_targets, optical_jammers, optical_recons)")

    return len(errors) == 0, errors


def get_default_config() -> Dict[str, Any]:
    """
    获取默认配置模板

    Returns:
        默认配置字典
    """
    return {
        "simulation": {
            "scenario_name": "默认光电对抗场景",
            "duration": 60.0,
            "time_step": 0.1,
            "data_count": 100,
            "output_types": ["static_images", "parameters"],
            "environment": {
                "weather_condition": "clear_weather",
                "temperature": 288.15,
                "humidity": 0.6,
                "pressure": 101325,
                "wind_speed": 5.0,
                "visibility": 10000
            }
        },
        "system": {
            "max_threads": 4,
            "image_resolution": [640, 480],
            "video_fps": 30,
            "random_seed": None
        },
        "optical_targets": [
            {
                "model": "默认红外目标",
                "position": {
                    "latitude": 39.9042,
                    "longitude": 116.4074,
                    "altitude": 1000.0
                },
                "observation_direction": {
                    "azimuth": 0.0,
                    "elevation": 0.0
                },
                "performance_params": {
                    "detection_range": 5000,
                    "resolution": 0.1,
                    "field_of_view": 10.0,
                    "spectral_range": [8e-6, 12e-6],
                    "sensitivity": 0.9
                },
                "work_mode": "passive_search"
            }
        ]
    }


def get_config_template(template_name: str = "basic") -> Dict[str, Any]:
    """
    获取指定的配置模板

    Args:
        template_name: 模板名称，可选值: basic, advanced, bad_weather, multi_device

    Returns:
        配置模板字典
    """
    templates = {
        "basic": get_default_config(),

        "advanced": {
            "simulation": {
                "scenario_name": "高级光电对抗场景",
                "duration": 120.0,
                "time_step": 0.05,
                "data_count": 500,
                "output_types": ["static_images", "dynamic_images", "parameters"],
                "environment": {
                    "weather_condition": "clear_weather",
                    "temperature": 288.15,
                    "humidity": 0.6,
                    "pressure": 101325,
                    "wind_speed": 5.0,
                    "visibility": 10000,
                    "atmospheric_turbulence": 0.2
                }
            },
            "system": {
                "max_threads": 8,
                "image_resolution": [1024, 768],
                "video_fps": 30,
                "random_seed": 12345
            },
            "optical_targets": [
                {
                    "model": "高级红外目标",
                    "position": {
                        "latitude": 39.9042,
                        "longitude": 116.4074,
                        "altitude": 1000.0
                    },
                    "observation_direction": {
                        "azimuth": 45.0,
                        "elevation": 10.0
                    },
                    "performance_params": {
                        "detection_range": 8000,
                        "resolution": 0.05,
                        "field_of_view": 15.0,
                        "spectral_range": [3e-6, 14e-6],
                        "sensitivity": 0.95
                    },
                    "work_mode": "active_tracking"
                }
            ],
            "optical_jammers": [
                {
                    "model": "激光干扰器",
                    "position": {
                        "latitude": 39.9000,
                        "longitude": 116.4000,
                        "altitude": 500.0
                    },
                    "jamming_direction": {
                        "azimuth": 0.0,
                        "elevation": 0.0
                    },
                    "performance_params": {
                        "jamming_power": 1000,
                        "coverage_range": 5000,
                        "beam_width": 2.0
                    },
                    "work_mode": "continuous",
                    "jamming_strategy": "laser_blinding"
                }
            ]
        },

        "bad_weather": {
            "simulation": {
                "scenario_name": "恶劣天气光电对抗场景",
                "duration": 150.0,
                "time_step": 0.2,
                "data_count": 300,
                "output_types": ["static_images", "parameters"],
                "environment": {
                    "weather_condition": "fog",
                    "temperature": 278.15,
                    "humidity": 0.95,
                    "pressure": 100800,
                    "wind_speed": 12.0,
                    "visibility": 500,
                    "cloud_cover": 1.0,
                    "precipitation": 5.0,
                    "atmospheric_turbulence": 0.8
                }
            },
            "system": {
                "max_threads": 4,
                "image_resolution": [640, 480],
                "video_fps": 15,
                "random_seed": 8901
            },
            "optical_targets": [
                {
                    "model": "恶劣天气红外目标",
                    "position": {
                        "latitude": 39.9042,
                        "longitude": 116.4074,
                        "altitude": 800.0
                    },
                    "observation_direction": {
                        "azimuth": 0.0,
                        "elevation": 5.0
                    },
                    "performance_params": {
                        "detection_range": 3000,
                        "resolution": 0.2,
                        "field_of_view": 25.0,
                        "spectral_range": [8e-6, 12e-6],
                        "sensitivity": 0.95,
                        "weather_penetration": 0.3
                    },
                    "work_mode": "weather_enhanced_search"
                }
            ]
        }
    }

    return templates.get(template_name, templates["basic"])


def create_simulation_summary(result: Dict[str, Any]) -> str:
    """
    创建仿真结果的文本摘要

    Args:
        result: run_simulation_api返回的结果字典

    Returns:
        格式化的文本摘要
    """
    if not result.get("success", False):
        return f"仿真执行失败: {result.get('error_info', '未知错误')}"

    session_info = result.get("session_info", {})
    config_info = result.get("simulation_config", {})
    output_info = result.get("output_structure", {})
    performance = result.get("performance_metrics", {})

    summary_lines = [
        "="*60,
        "光电对抗仿真系统执行摘要",
        "="*60,
        "",
        "会话信息:",
        f"  会话ID: {session_info.get('session_id', 'N/A')}",
        f"  开始时间: {session_info.get('start_time', 'N/A')}",
        f"  结束时间: {session_info.get('end_time', 'N/A')}",
        f"  执行时长: {session_info.get('duration', 0):.2f} 秒",
        f"  输出目录: {session_info.get('output_directory', 'N/A')}",
        "",
        "仿真配置:",
        f"  场景名称: {config_info.get('scenario_name', 'N/A')}",
        f"  仿真时长: {config_info.get('duration', 0)} 秒",
        f"  数据数量: {config_info.get('data_count', 0)}",
        f"  输出类型: {', '.join(config_info.get('output_types', []))}",
        "",
        "设备配置:",
    ]

    device_count = config_info.get('device_count', {})
    for device_type, count in device_count.items():
        summary_lines.append(f"  {device_type}: {count} 个")

    summary_lines.extend([
        "",
        "输出结果:",
        f"  文件总数: {output_info.get('total_files', 0)}",
        f"  总大小: {output_info.get('total_size', 0) / 1024 / 1024:.2f} MB",
        ""
    ])

    categories = output_info.get('categories', {})
    for category, info in categories.items():
        if info.get('count', 0) > 0:
            summary_lines.append(f"  {category}: {info['count']} 个文件 ({info['size'] / 1024:.1f} KB)")

    summary_lines.extend([
        "",
        "性能指标:",
        f"  处理速度: {performance.get('processing_speed', 0):.2f} 数据/秒",
        f"  文件生成速度: {performance.get('files_per_second', 0):.2f} 文件/秒",
        f"  使用线程数: {performance.get('thread_count', 0)}",
        "",
        "="*60
    ])

    return "\n".join(summary_lines)


if __name__ == "__main__":
    """
    API测试示例
    """
    import sys

    def test_basic_simulation():
        """测试基本仿真功能"""
        print("开始基本仿真测试...")

        # 使用默认配置进行测试
        test_config = get_default_config()

        # 修改配置以加快测试速度
        test_config["simulation"]["data_count"] = 10
        test_config["simulation"]["duration"] = 10.0

        result_json = run_simulation_api(
            config_input=test_config,
            log_level='INFO'
        )

        # 解析JSON结果
        result = json.loads(result_json)

        print(f"仿真结果: {'成功' if result['success'] else '失败'}")
        if result['success']:
            print(f"输出目录: {result['session_info']['output_directory']}")
            print(f"执行时间: {result['session_info']['duration']:.2f} 秒")
            print(f"生成文件数: {result['output_structure']['total_files']}")

            # 打印详细摘要
            summary = create_simulation_summary(result)
            print("\n" + summary)
        else:
            print(f"错误信息: {result['error_info']}")

        return result

    def test_config_validation():
        """测试配置验证功能"""
        print("\n开始配置验证测试...")

        # 测试有效配置
        valid_config = get_default_config()
        is_valid, errors = validate_config_dict(valid_config)
        print(f"有效配置验证: {'通过' if is_valid else '失败'}")
        if errors:
            print(f"错误: {errors}")

        # 测试无效配置
        invalid_config = {"invalid": "config"}
        is_valid, errors = validate_config_dict(invalid_config)
        print(f"无效配置验证: {'通过' if not is_valid else '失败'}")
        print(f"预期错误: {errors}")

    def test_config_templates():
        """测试配置模板功能"""
        print("\n开始配置模板测试...")

        templates = ["basic", "advanced", "bad_weather"]
        for template_name in templates:
            config = get_config_template(template_name)
            is_valid, errors = validate_config_dict(config)
            print(f"{template_name} 模板: {'有效' if is_valid else '无效'}")
            if errors:
                print(f"  错误: {errors}")

    def main():
        """主测试函数"""
        print("光电对抗仿真系统API测试")
        print("="*50)

        try:
            # 测试配置验证
            test_config_validation()

            # 测试配置模板
            test_config_templates()

            # 测试基本仿真（可选，因为需要较长时间）
            if len(sys.argv) > 1 and sys.argv[1] == "--full-test":
                result = test_basic_simulation()

                # 如果仿真成功，展示输出结构
                if result.get('success'):
                    print("\n输出文件结构:")
                    output_structure = result['output_structure']
                    for category, info in output_structure['categories'].items():
                        if info['count'] > 0:
                            print(f"\n{category} ({info['count']} 个文件):")
                            for file_info in info['files'][:5]:  # 只显示前5个文件
                                print(f"  - {file_info['name']} ({file_info['size']} bytes)")
                            if len(info['files']) > 5:
                                print(f"  ... 还有 {len(info['files']) - 5} 个文件")
            else:
                print("\n跳过完整仿真测试（使用 --full-test 参数运行完整测试）")

            print("\n所有测试完成!")

        except Exception as e:
            print(f"测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

    main()
