﻿|<a name="mbookmark10"></a>标识：EMDG-MXSJ|编号：||
| :- | :- | :- |
|版本：V0.4|密级：公开||
|<p>**“基于场景驱动的电磁多模态数据集生成软件”**</p><p>**技术文件**</p>|||
||||
|<p>基于场景驱动的电磁多模态数据集生成</p><p>软件模型设计文档</p>|||
|<p></p><p></p><p></p><p></p><p></p><p></p><p>	</p>|||
|<p><a name="编制时间_f"></a>安徽大学</p><p>2025年07月</p>|||

![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.001.png)


<a name="密级_q"></a><a name="mbookmark20"></a> 

||
| - |
|<a name="文件名称_q"></a>基于场景驱动的电磁多模态数据集生成软件模型设计文档|
|签署页|
|<p><a name="_toc309218923"></a><a name="_toc313611707"></a><a name="_toc309218719"></a><a name="_toc303156943"></a>编制：           日期：           </p><p><a name="_toc313611708"></a><a name="_toc309218924"></a><a name="_toc303156944"></a><a name="_toc309218720"></a>校对：           日期：           </p><p><a name="_toc303156945"></a><a name="_toc313611709"></a><a name="_toc309218721"></a><a name="_toc309218925"></a>审核：           日期：           </p><p><a name="_toc313611710"></a><a name="_toc309218926"></a><a name="_toc303156946"></a><a name="_toc309218722"></a>标审：           日期：           </p><p><a name="_toc309218927"></a><a name="_toc303156947"></a><a name="_toc309218723"></a><a name="_toc313611711"></a>会签：           日期：           </p><p>批准：           日期：           </p>|



<a name="_toc27664525"></a><a name="mbookmark30"></a>修 改 页

|序号|版本号|修改内容描述|修改人|日期|备注|
| :-: | :-: | :-: | :-: | :-: | :-: |
|1|V0.1|编写模型设计文档初稿|李坤|2025\.07.08||
|2|V0.2|根据内审意见修改|李坤|2025\.07.10||
|3|V0.3|根据评审小组意见修改|李坤|2025\.07.11||
|4|V0.4|根据专家评审意见修改|李坤|2025\.07.31||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||


目  次

[**1 范围	**1****](#_toc6972)

[1.1 标识	1](#_toc10230)

[1.2 模型概述	1](#_toc2981)

[1.2.1 模型算法设计目的	1](#_toc26421)

[1.2.2 模型算法设计依据	1](#_toc5147)

[1.2.3 模型算法功能组成	1](#_toc5207)

[1.2.4 模型算法集成应用	2](#_toc28104)

[1.2.5 运行环境需求	2](#_toc17979)

[1.3 文档概述	3](#_toc29950)

[**2 引用文档	**3****](#_toc19729)

[**3 模型设计	**3****](#_toc12720)

[3.1 机载电子目标数据产生模型	3](#_toc12160)

[3.1.1 模型描述	3](#_toc7292)

[3.1.2 实现设计	3](#_toc9713)

[3.1.2.1 通信目标数据产生算法模型	3](#_toc19783)

[3.1.2.2 雷达目标数据产生算法模型	12](#_toc11602)

[3.1.2.3 光电目标数据产生算法模型	22](#_toc26493)

[3.2 干扰设备数据产生模型	26](#_toc21705)

[3.2.1 模型描述	26](#_toc31080)

[3.2.2 实现设计	26](#_toc17972)

[3.2.2.1 通信干扰数据产生算法模型	26](#_toc28485)

[3.2.2.2 雷达干扰数据产生算法模型	30](#_toc18278)

[3.2.2.3 光电干扰数据产生算法模型	34](#_toc32765)

[3.3 电子对抗侦察设备数据产生模型	39](#_toc8825)

[3.3.1 模型描述	39](#_toc26798)

[3.3.2 实现设计	39](#_toc5569)

[3.3.2.1 通信对抗侦察设备数据产生算法模型	39](#_toc13668)

[3.3.2.2 雷达对抗侦察设备数据产生算法模型	48](#_toc12306)

[3.3.2.3 光电对抗侦察设备数据产生算法模型	61](#_toc2228)

[3.4 雷达探测模型数据产生模型	64](#_toc25721)

[3.4.1 模型描述	64](#_toc8472)

[3.4.2 实现设计	64](#_toc14501)

[3.4.2.1 应用场景	64](#_toc4663)

[3.4.2.2 总体思路	65](#_toc19329)

[3.4.2.3 输入输出数据	65](#_toc6541)

[3.4.2.4 流程图	67](#_toc7647)

[3.4.2.5 步骤详细说明	68](#_toc28925)

[3.4.2.6 算法详细介绍	69](#_toc5398)

[3.5 多节点融合数据生成	90](#_toc22363)

[3.5.1 模型描述	90](#_toc12298)

[3.5.2 实现设计	90](#_toc1527)

[3.5.2.1 多节点雷达侦察数据融合算法模型	90](#_toc26869)

[3.5.2.2 多节点探测数据融合算法模型	95](#_toc17311)

[3.5.2.3 多节点通信侦察数据融合	104](#_toc21218)

[**4 注释	**111****](#_toc2245)


**\
图目录

图 1  通信目标数据产生算法模型流程图	[7](#_toc22971)

图 2  AM调制模型	[8](#_toc7821)

图 3  FM调制实现结构	[9](#_toc19953)

图 4  FSK实现框图	[9](#_toc2816)

图 5  BPSK实现框图	[10](#_toc31197)

图 6  MSK实现框图	[11](#_toc10792)

图 7  GMSK产生步骤	[12](#_toc26117)

图 8  雷达目标数据产生算法模型流程图	[17](#_toc4708)

图 9  天线波束扫描示意图	[19](#_toc29283)

图 10  光电目标数据产生算法模型流程图	[24](#_toc28430)

图 11  通信干扰数据产生算法模型流程图	[28](#_toc19907)

图 12  雷达干扰数据产生算法模型流程图	[32](#_toc21248)

图 13  光电干扰数据产生算法模型流程图	[37](#_toc31852)

图 14  通信对抗侦察设备数据产生算法模型流程图	[43](#_toc5206)

图 15  无线电侦察接受算法	[44](#_toc12802)

图 16  通信调制方式识别算法	[45](#_toc5494)

图 17  雷达对抗侦察设备数据产生流程图	[55](#_toc683)

图 18  侦察参数测量算法流程图	[57](#_toc8014)

图 19  信道划分	[57](#_toc12145)

图 20  信道化多相结构	[58](#_toc12509)

图 21  雷达分选算法流程图	[60](#_toc4584)

图 22  光电对抗侦察设备数据产生算法模型流程图	[62](#_toc26613)

图 23  雷达探测模型数据产生模型流程图	[68](#_toc27408)

图 24  典型的CHIRP信号	[73](#_toc26431)

图 25  LFM信号的时域波形和幅频特性	[74](#_toc6524)

图 26  脉冲压缩处理流程图	[74](#_toc23935)

图 27  LFM信号的匹配滤波	[75](#_toc31412)

图 28  匹配滤波的输出信号	[75](#_toc4633)

图 29   LFM信号的匹配滤波	[76](#_toc31705)

图 30  地杂波的功率谱	[77](#_toc6869)

图 31  一次对消滤波器结构	[78](#_toc7559)

图 32   MTI滤波器	[79](#_toc7903)

图 33  CFAR平面	[80](#_toc28415)

图 34  二维CFAR原理图	[80](#_toc19218)

图 35  航迹跟踪	[83](#_toc17542)

图 36  卡尔曼滤波器的算法框图	[84](#_toc23413)

图 37  两种波门示意	[88](#_toc11528)

![ref1]图 38  椭圆波门（）	[89](#_toc24070)

图 39  航迹转换关系示意图	[90](#_toc27393)

图 40  多节点雷达侦察数据融合算法模型流程图	[92](#_toc4791)

图 41  多节点探测数据融合算法模型流程图	[97](#_toc29854)

图 42  串行合并流程	[102](#_toc3255)

图 43  多节点通信侦察数据融合算法模型流程图	[106](#_toc16159)



表目录

[表 1  模型算法集成应用表	2](#_toc7798)

[表 2  引用文档	3](#_toc27455)

[表 3  输入数据	4](#_toc29705)

[表 4  输出数据	5](#_toc31486)

[表 5  FSK调制映射关系	9](#_toc2255)

[表 6  离散电平值	11](#_toc27277)

[表 7  输入数据	13](#_toc27456)

[表 8  输出数据	16](#_toc3917)

[表 9  SWERLING模型	19](#_toc2018)

[表 10  输入数据	22](#_toc1804)

[表 11  输出数据	23](#_toc8474)

[表 12  输入数据	27](#_toc3708)

[表 13  输入数据	28](#_toc31042)

[表 14  输入数据	31](#_toc7139)

[表 15  输出数据	32](#_toc25339)

[表 16  输入数据	35](#_toc4629)

[表 17  输出数据	35](#_toc11205)

[表 18  输入数据	40](#_toc8096)

[表 19  输出数据	42](#_toc5325)

[表 20  输入数据	49](#_toc7750)

[表 21  输出数据	54](#_toc19973)

[表 22  输入数据	61](#_toc1712)

[表 23  输出数据	62](#_toc10106)

[表 24  输入数据	65](#_toc19552)

[表 25  输出数据	67](#_toc27875)

![ref2][表 26  维量测落入波门内的概率	89](#_toc13800)

[表 27  输入数据	91](#_toc8053)

[表 28  输出数据	91](#_toc1369)

[表 29  输入数据	96](#_toc511)

[表 30  输出数据	96](#_toc1279)

[表 31  输入数据	105](#_toc11058)

[表 32  输出数据	105](#_toc19531)




vi

1  <a name="_toc262486440"></a><a name="_toc519172131"></a><a name="_toc395703978"></a><a name="_toc27664526"></a><a name="_toc6972"></a><a name="_toc257209912"></a><a name="_toc470502522"></a><a name="_toc102636342"></a><a name="_toc527469909"></a><a name="_toc519170968"></a><a name="_toc434477262"></a><a name="_toc391651999"></a><a name="mbookmark60"></a>范围
   1  <a name="_toc10230"></a><a name="_toc6152"></a><a name="_toc102636344"></a><a name="_toc262486441"></a><a name="_toc527469910"></a><a name="_toc27664527"></a><a name="_toc257209913"></a><a name="_toc102636343"></a><a name="_toc395703979"></a><a name="_toc434477263"></a><a name="_toc470502523"></a><a name="_toc519172132"></a><a name="_toc519170969"></a>标识
1. 本文档的标题：基于场景驱动的电磁多模态数据集生成软件模型设计文档；
1. 本文档的标识：EMDG-MXSJ；
1. 本文档标识：V0.4
1. 本文档适用的软件：基于场景驱动的电磁多模态数据集生成软件
   1  <a name="_toc2981"></a>模型概述
      1  <a name="_toc6833"></a><a name="_toc26421"></a>模型算法设计目的

         本系列算法旨在构建电子战全流程仿真体系，通过通信目标/干扰数据生成、雷达探测/侦察仿真、多源数据融合等核心模型，实现从信号层到情报层的电子对抗全要素模拟。重点解决复杂电磁环境下目标信号模拟、干扰效果评估、多站异构数据融合等关键问题，为通信对抗、雷达对抗等电子战系统的效能验证、战术推演和装备测试提供高保真仿真环境，支撑电子战装备研发、作战训练和战术决策全流程需求。

      1  <a name="_toc5147"></a><a name="_toc29681"></a>模型算法设计依据
1) 基于场景驱动的电磁多模态数据集生成软件项目合同
1) 基于场景驱动的电磁多模态数据集生成软件需求规格说明
   1  <a name="_toc5207"></a><a name="_toc19773"></a>模型算法功能组成

      <a name="_toc25638"></a>《基于场景驱动的电磁多模态数据集生成软件需求规格说明》中功能有仿真引擎调用、数据协议交互、机载电子目标数据产生、干扰设备数据产生，电子对抗侦察设备数据产生、雷达探测模型数据产生、单站点分选识别、多节点融合数据生成、数据评估分析等功能。

      基于场景驱动的电磁多模态数据集生成软件的功能是由13个算法模型支持完成，具体算法如下。

1) 通信目标数据产生算法模型
1) 雷达目标数据产生算法模型
1) 光电目标数据产生算法模型
1) 通信干扰数据产生算法模型
1) 雷达干扰数据产生算法模型
1) 光电干扰数据产生算法模型
1) 通信对抗侦察设备数据产生算法模型
1) 雷达对抗侦察设备据产生算法模型
1) 光电对抗侦察设备数据产生算法模型
1) 雷达探测模型数据产生算法模型
1) 多节点雷达侦察数据融合算法模型
1) 多节点探测数据融合算法模型
1) 多节点通信侦察数据融合算法模型

1  <a name="_toc28104"></a>模型算法集成应用

   表 1<a name="_toc27973"></a><a name="_toc7798"></a> 模型算法集成应用表

   |序号|需求功能|调用的算法模型|
   | :-: | :-: | :-: |
   ||仿真引擎调用|/|
   ||数据协议交互|/|
   ||机载电子目标数据产生|<p>通信目标数据产生算法模型</p><p>雷达目标数据产生算法模型</p><p>光电目标数据产生算法模型</p>|
   ||干扰设备数据产生|<p>通信干扰数据产生算法模型</p><p>雷达干扰数据产生算法模型</p><p>光电干扰数据产生算法模型</p>|
   ||电子对抗侦察设备数据产生|<p>通信对抗侦察设备数据产生算法模型</p><p>雷达对抗侦察设备数据产生算法模型</p><p>光电对抗侦察设备数据产生算法模型</p>|
   ||雷达探测模型数据产生|雷达探测模型数据产生算法模型|
   ||单站点分选识别|/|
   ||多节点融合数据生成|<p>多节点雷达侦察数据融合算法模型</p><p>多节点探测数据融合算法模型</p><p>多节点通信侦察数据融合算法模型</p>|
   ||数据评估分析|/|

1  <a name="_toc17979"></a><a name="_toc308"></a>运行环境需求
1) 操作系统需求

   Windows 10/11 64位

   Linux (Ubuntu 20.04 )

1) 开发环境

   Python: 3.7-3.10 (推荐3.8)

   VS2019

1) 开发语言

   开发语言C++ 、Python

   1  <a name="_toc251744109"></a><a name="_toc274732421"></a><a name="_toc250991160"></a><a name="_toc274732422"></a><a name="_toc250991275"></a><a name="_toc251747932"></a><a name="_toc29582"></a><a name="_toc251744110"></a><a name="_toc250991276"></a><a name="_toc251747931"></a><a name="_toc29950"></a><a name="_toc527469911"></a><a name="_toc257209914"></a><a name="_toc27664528"></a><a name="_toc519170970"></a><a name="_toc470502524"></a><a name="_toc262486442"></a><a name="_toc395703980"></a><a name="_toc434477264"></a><a name="_toc519172133"></a>文档概述

      本文档是基于场景驱动的电磁多模态数据集生成软件项目的模型设计文档，描述该项目算法模型的应用场景、功能描述、输入输出等，是进行软件设计、开发和实现的基础。

   1  <a name="_toc19729"></a>引用文档

      <a name="_toc101864109"></a><a name="_ref514145528"></a>表 2<a name="_toc27455"></a> 引用文档

|序号|文档标识|文档名称|来源|
| - | :-: | :-: | :-: |
|1|KYWX2025015|基于场景驱动的电磁多模态数据集生成软件项目合同|国防科技大学科研部|
|<a name="_toc12720"></a>2|EMDG-SRS|基于场景驱动的电磁多模态数据集生成软件需求规格说明|安徽大学|

1  模型设计
   1  <a name="_toc12160"></a>机载电子目标数据产生模型
      1  <a name="_toc7292"></a>模型描述

         机载电子目标数据产生模块包含通信目标数据产生算法模型、雷达目标数据产生算法模型、光电目标数据产生算法模型三个子模型，通过这三个子模型实现机载电子目标数据产生的功能需求。

      1  <a name="_toc9713"></a>实现设计
         1  <a name="_toc19783"></a>通信目标数据产生算法模型
            1  应用场景

               本模型用于电子对抗装备体系效能评估中的通信干扰验证环节：在典型陆战场场景下，根据蓝方电台参数实时生成目标通信IQ信号，叠加红方干扰设备产生的同频干扰IQ，并按地形信道模型完成衰减，最终输出带标签的受扰数据流供在线解调计算误码率，直接支撑“通信中断/降效”判定。

            1  总体思路

               本算法主要分为四部分。第一部分，根据输入的参数调制产生通信IQ信号。第二部分，根据输入的参数调用通信电磁干扰设备IQ数据产生模型产生干扰IQ数据，并于通信IQ数据叠加，第三部分，根据作战场景对信号进行衰减。第四部分，对最终得到的信号进行解调评估性能指标。

            1  输入输出数据

               表 3<a name="_toc29705"></a> 输入数据

               |<a name="_hlk203037434"></a>字段|类型|说明|可选值|参考值|备注|
               | :-: | :-: | :-: | :-: | :-: | :-: |
               |comm\_pre\_model|string|预定义模型|<p>"comm\_pre\_model\_none"：无预定义模型</p><p>“comm\_pre\_model\_1”:预定义模型1</p><p>“comm\_pre\_model\_2”:预定义模型2</p><p>“comm\_pre\_model\_sound”:声音预定义模型</p>|||
               |Tx\_Altitude|double|发射机高度||1000\.0||
               |Tx\_Longitude|double|发射机经度||120\.0||
               |Tx\_Latitude|double|发射机纬度||30\.0||
               |Tx\_power|double|功率 dBm||50||
               |Tx\_comm\_freq|double|频率 MHz||20||
               |Tx\_comm\_type|int|调制方式|<p>0:ASK </p><p>1:FSK </p><p>2:PSK </p><p>3:QAM </p><p>4:MSK </p><p>5:AM </p><p>6:GMSK </p><p>7:FM </p><p>8:G711</p>|0||
               |Tx\_code\_type|int|编码类型|<p>0:绝对码</p><p>1:相对码</p>|0||
               |Tx\_work\_mode|int|工作模式|<p>0:常规</p><p>1:调频通信</p><p>2:扩频通信</p>|0||
               |Tx\_SymbolRate|double|码数率||1000000||
               |Tx\_am\_type|int|AM调制方式|<p>0:AM </p><p>1:DSB </p><p>2:USB </p><p>3:LSB</p>|0||
               |Tx\_fsk\_type|int |FSK类型|<p>0:FSK\_2 </p><p>1:FSK\_4</p>|0||
               |Tx\_psk\_type|int|PSK类型|<p>0:PSK\_2 </p><p>1:PSK\_4 </p><p>2:PSK\_8</p>|0||
               |Tx\_qam\_type|int|QAM调制方式|<p>0:QAM16 </p><p>1:QAM64 </p><p>2:QAM256</p>|0||
               |Rx\_Altitude|double|接收机高度||1000\.0||
               |Rx\_Longitude|double|接收机经度||120\.0||
               |Rx\_Latitude|double|接收机纬度||30\.0||
               |Rx\_Gr\_db|double|接收机天线增益 dB||1\.0||
               |is\_jam|bool|是否添加干扰||false||
               |comm\_jam\_pre\_model|string|干扰预定义模型|<p>“comm\_jam\_pre\_model\_none”: 无预定义模型</p><p>“comm\_jam\_pre\_model\_noise”: 噪声干扰机</p><p>“comm\_jam\_pre\_model\_jam”: 欺骗干扰机</p>|||
               |jam\_Longitude|double|干扰机经度||120\.0||
               |jam\_Latitude|double|干扰机纬度||30\.0||
               |jam\_Altitude|double|干扰机高度||1000||
               |jam\_range|double|干扰范围||10000||
               |jam\_power|double|干扰功率 dBm||10||
               |jam\_bandwidth|double|干扰带宽 MHz||20||
               |jam\_mode|int|干扰模式|<p>0:阻塞 </p><p>1:瞄频</p><p>2:扫频</p>|0||
               |jam\_type|int|干扰类型|<p>0:脉冲干扰</p><p>1:调频噪声 </p><p>2:卷积噪声</p><p>3:音调干扰</p><p>4:线性调频干扰</p>|1||
               |jam\_tone\_freq|souble|音调干扰频点 MHZ||[20, 30, 40, 50, 60]||
               |jam\_sweep\_start\_freq|double|扫频开始频点 MHZ||10||
               |jam\_sweep\_end\_freq|double|扫频结束频点 MHZ||50||
               |jam\_sweep\_time|double|扫频时间 us||30||
               表 4<a name="_toc31486"></a> 输出数据

               |字段|类型|说明|备注|
               | :-: | :-: | :-: | :-: |
               |SNR|double|信噪比 dB||
               |BER|double|误码率||
               |PDR|double|丢包率||
               |delay|double|时延||
               |throughput|double|吞吐量 bps||
               |network\_delay|double|组网时延||
               |is\_network|bool|组网情况||
               |comm\_status|bool|通信状态||
               |SQI|int|通信质量|<p>0:Excellent, </p><p>1:Good, </p><p>2:Marginal, </p><p>3:Poor</p>|
               |jam|bool|是否被干扰||
               |hard\_measures|int|硬抗措施|<p>0: 无硬抗措施</p><p>1: 盲发电报</p><p>2: 强行作报</p><p>3: 隐蔽作报</p><p>4: 暂时静默后突发通信</p>|
               |comm\_system|int|通信体制|<p>0:ASK </p><p>1:FSK </p><p>2:PSK </p><p>3:QAM </p><p>4:MSK </p><p>5:AM </p><p>6:GMSK </p><p>7:FM </p><p>8:G711</p>|
               |SER|double|误比特率||
            1  流程图

               IQ信号产生流程图如[图 1](#_ref1344)所示，信号处理流程图如所示。

               ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.004.png)

               图 1 IQ信号产生流程图

               ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.005.png)

               <a name="_ref1344"></a>图 2<a name="_toc22971"></a> 信号处理流程图

            1  步骤详细说明

               IQ信号产生：

1. 读取输入参数，依次接收：调制类型（ASK / 2FSK / 4FSK / 2PSK / QPSK / 8PSK / 16QAM / 64QAM / 256QAM / MSK / AM / DSB / USB / LSB）、编码类型（绝对编码或相对编码）、发射码元速率 Rs、载波频率 fc、发射功率 P（dBm）以及待传输的二进制比特流 b；
1. 根据调制类型确定每个符号携带的比特数 k，将比特流 b 按 k 位一组顺序切分，得到符号索引序列 s；
1. 进行编码映射，若编码类型为“绝对编码”，符号索引 s 保持不变，若为“相对编码”，则对 s 进行差分处理；
1. 进行星座映射，把差分后的符号索引映射为复数符号 d；
1. 使用滤波器对符号序列进行成形，先以系统采样率fs对符号序列进行上采样，再与滤波器卷积，得到平滑的基带复波形IQ\_base(t)；
1. 将基带信号搬移至指定的载波频率，在数字正交上变频；
1. 把数字幅度归一到目标发射功率，计算当前序列的RMS值并做归一化，得到的复采样序列即为通信IQ信号。

   信号处理：

1) 根据输入参数生成通信IQ信号；
1) 根据输入决定是否添加干扰，如果添加干扰，根据干扰类型干扰设备的经度、纬度、高度，干扰范围，干扰功率，以及对于不同干扰类型设置的参数，调用通信电磁干扰设备IQ数据产生模型产生干扰IQ数据，叠加到通信IQ信号上；
1) 根据发射机和接收机的经度、纬度、高度，以及发射功率和接收天线增益，计算信号的场景衰减，并作用于IQ信号上；
1) 对接收到的IQ信号进行解调，计算其信噪比、误码率、丢包率、时延、吞吐量，综合通信状态参数数据以及IQ数据集，合并为通信目标数据集输出。
   1  算法详细介绍
      1  AM调制

         通过基带信号控制载波的幅度实现AM调制，AM信号的调制模型如[图 2](#_ref16702)所示。

         ![E:\项目非密\射频15\20180707\图片\a0.png](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.006.png)

         <a name="_ref16702"></a><a name="_ref201070751"></a>图 3<a name="_toc7821"></a> AM调制模型

         M(t) 为基带信号，它可以是确知的信号，也可以是随机信号，但通常认为它的平均值为0，载波为Tcos=A0×cos(wt+φ0)	

         式中，A0为载波振幅，ωc为载波角频率，φ\_0为载波的初始相位。

         AM信号时域表达式为：s(![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.007.png)t)=(1+M(t))×Tcos

      1  FM调制

         将调制信号加到频率源的控制端，使频率源的输出频率随调制信号的变化而变化，生成调制波，振幅保持恒定。

         载波信号为Sc=Accos(wct+∅c)

         其中，Ac为载波幅度，ωc为载波角频率，φc为载波初始相位。

         调制信号为Sm=Amcos(wmt+∅m)

         其中，Am为调制信号幅度，ωm为调制信号频率，φm相调制信号初始相位。

         按比例常数K，产生的FM信号为：

         St=Accos(ωct+K×Sm+ φc)

         FM调制实现结构如图所示。

         ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.008.png)

         图 4<a name="_toc19953"></a> FM调制实现结构

      1  FSK调制

         在FSK调制中，载波频率随输入s(t)信号的比特0或比特1变化。输入比特0对应于载波频率fc−∆f，输入比特1对应于载波频率fc+∆f

         以4FSK调制为例，输入的信息比特序列以每二个比特为一组操作。载波频率共有4个可能的取值，分别对应输入比特对{00},{01},{10}和{11}。

         频率偏移量为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.009.png)的![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.010.png)倍（![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.010.png)为整数）。α=2m−1−M，式中M为调制维数，对于4FSK，M=4,m=1,2,3,4。计算得α=±1,±3，输入的信息比特对与载波频率的调制映射关系如[表 5](#_ref16643)所示。

         <a name="_ref16643"></a>表 5<a name="_toc2255"></a> FSK调制映射关系

         |信息比特对|载波频率|信息比特对|载波频率|
         | :-: | :-: | :-: | :-: |
         |00|![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.011.png)|10|![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.012.png)|
         |01|![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.013.png)|11|![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.014.png)|

         M个振荡器工作在不同频率fc+α∆f![ref3]上，调制器每隔时间Ts![ref3]就依照输入的信息比特切换到相应的频率分路。4FSK实现框图如[图 4](#_ref16594)所示。

         ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.016.png)

         <a name="_ref16594"></a><a name="_ref201064662"></a>图 5<a name="_toc2816"></a> FSK实现框图

      1  PSK调制

         在BPSK调制中，输入的信息比特序列共有2种可能的取值0或者1，分别对应2种不同的载波相位变化。

         当输入比特为0时，调制信号Sm(t)=Amcos(2πfct)，其中![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.017.png)是载波幅度![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.018.png)是载波频率；输入比特为1时，调制信号Sm=−Amcos(2πfct)，分别对应0°和180°载波相位变化。给出了BPSK的输入信息比特和载波相位变化之间的关系。

         BPSK实现框图如[图 5](#_ref16568)所示。

         ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.019.png)

         <a name="_ref16568"></a>图 6<a name="_toc31197"></a> BPSK实现框图

      1  MSK调制

         MSK调制信号的实现步骤如下：

1) 根据输入MSK调制器的信息比特计算出![ref3]αk。当输入为比特0时，αk=−1![ref3]当输入为比特1时，![ref3]αk=1；
1) 设初始相位![ref3]ϕ0为0，按输入信息比特的顺序依次计算                ![ref3]ϕk=ϕk−1,当αk−1=αkϕk−1±kπ,当αk−1≠αk，k为信息比特的序号；
1) 接下来，计算同相分量![ref3]Ik=−cosΦk−1, k 为 奇 数cosΦk−1, k 为 偶 数。正交分量![ref3]*Qk*=*akIk*\
   ；
1) 得到同相分量![ref3]![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.020.png)和正交分量![ref3]![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.021.png)后, 便于对![ref3]![ref4]路和Q路分别处理；
1) 对![ref3]![ref4]路进行余弦![ref3]cos*πt*2*T*b加权；对Q路进行正弦![ref3]sin*πt*2*Tb*加权；
1) 最后，利用正交合成得到输出的已调信号波形。

   MSK实现框图如[图 6](#_ref16470)所示。

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.023.png)

   <a name="_ref16470"></a>图 7<a name="_toc10792"></a> MSK实现框图

   1  QAM调制

      QAM调制实现步骤如下：

1) 输入比特流分组：将二进制比特流每k位分为一组（k为偶数）

   星座阶数M=2k

1) 串并转换

   每组k位拆分为两路,I路为前k/2位，Q路为后k/2位

1) 星座映射

   将每路k/2位映射为离散电平值（星座点）

   表 6<a name="_toc27277"></a> 离散电平值

   |**二进制**|**电平值**Ik**或**Qk|
   | :-: | :-: |
   |00|-3|
   |01|-1|
   |11|+1|
   |10|+3|

1) 载波调制

   I路乘以余弦载波cos⁡(2πfct)，Q路乘以负正弦载波−sin⁡(2πfct)

1) 信号合成

   将I路和Q路调制后的信号相加，得到最终QAM信号

   St=Ikcos2πfct−Qksin⁡(2πfct)

   1  ASK调制

      ASK调制实现步骤如下：

1) 输入编码预处理，将编码变为单极性码
1) 通过上采样成形滤波后，与载波相乘

   S2ASK=mt⋅cos⁡(2πfct)

   1  GMSK调制

      GMSK 是一种基于频移键控的调制形式,是基于MSK的调制方式

      GMSK产生步骤如下：

      ![图片1](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.024.png)

      图 8<a name="_toc26117"></a> GMSK产生步骤

   1) 将码元数据编码处理上采样后通过高斯滤波器
   1) 通过MSK调制生成GMSK信号SGMSK
      1  G711通信

         G.711是ITU-T标准的语音编解码器，采用对数压扩（companding）技术，其流程如下：

1) 信号采样，获取信号x(n)
1) 压扩量化

   将13/14位线性PCM压缩为8位非线性PCM，使用对数曲线保留动态范围。

   本算法使用A-law 公式

   Fx=Ax1+lnA⋅sgnx0≤x<1A1+lnAx1+lnA⋅sgnx1A≤x≤1

   其中A=87.6

1) 将量化值转为8位二进制码
1) 通过2PSK调制输出信号
   1  <a name="_toc11602"></a>雷达目标数据产生算法模型
      1  应用场景

         本算法核心功能是生成逼真的、信号级别的雷达目标原始回波数据（以复IQ信号形式输出）。其主要应用场景包括：

1) 雷达系统仿真与性能评估：作为大型雷达系统级仿真（包括电子战对抗仿真）中的关键模块，提供高保真的目标回波信号输入。评估整个雷达系统在不同作战场景、目标特性（RCS起伏模型）、环境（地形、气象、杂波）和电子对抗条件下的探测性能（探测概率、虚警概率等）。进行雷达系统参数（如波形参数、发射功率、天线方向图）的优化研究；
1) 雷达接收机硬件测试与验证：作为雷达接收机（尤其是数字接收机/Digital Receiver或软件定义雷达/SDR的基带处理部分）的测试激励源。模拟各种典型和极端的目标、环境（杂波、干扰）条件，测试接收机链路的动态范围、线性度、抗干扰能力以及ADC（模数转换器）性能。辅助进行接收机校准和功能验证。
1) 雷达操作员培训与教学：生成用于雷达操作员培训系统的模拟回波数据，创建逼真的训练场景（如不同目标类型、数量、运动轨迹、干扰样式）。
   1  总体思路

      首先脉内采取可控带宽、脉宽、载频的线性调频信号；脉冲重复频率可选固定重频、参差、滑变、抖动；载频可选固定、捷变、跳变；脉宽可选固定、可变；雷达的工作模式可选扫描、跟踪、休眠；模拟的起伏模型可选无起伏、Swerling1~4型；最后由场景位置参数添加信号幅度调制，完成雷达目标数据产生。

   1  输入输出数据

      表 7<a name="_toc27456"></a> 输入数据

      |字段|类型|说明|可选值|参考值|备注|
      | :-: | :-: | :-: | :-: | :-: | :-: |
      |radar\_tar\_pre\_model|string|预定义模型|<p>radar\_tar\_pre\_modle\_none：无预设模型</p><p>radar\_tar\_pre\_modle\_1：雷达目标模型1</p><p>radar\_tar\_pre\_modle\_2：雷达目标模型2</p>|||
      |sig\_mode\_inst|int|脉内|0：LFM|0||
      |pulse\_mode\_inst|int|脉间|<p>0：固定</p><p>1：脉组参差</p><p>2：脉间参差</p><p>3：滑变</p><p>4：抖动</p><p>5：驻留</p>|0||
      |LFMSigBw|double|带宽MHz||10\.0||
      |LFMSigPw|double|脉宽 us||20\.0||
      |LFMFre|double|中心频点MHz||0\.0||
      |SNR\_signal|double|信噪比||3\.0||
      |sigFixedPRF\_PRF|double|固定重频值Hz||1000\.0||
      |sigFixedPRF\_PulseNum|double|固定重频下的脉冲个数	||100||
      |PRFValues|数组|脉组参差的所有PRF值Hz||[500,700,900]||
      |pulseNumsPerPRF|Array|对应PRF的脉冲数||[5,7,9]||
      |sigStaggeredPRF\_RoundNumber|double|脉组参差下的发射轮数||2||
      |PRFValues\_Interval|Array|脉间参差的PRF值Hz||[500, 700, 900]||
      |sigStaggeredPRF\_Interval\_RoundNumber|int|脉间参差发射轮数||30||
      |SlidePRF\_start|double|滑变开始频率 Hz||800||
      |SlidePRF\_end|double|滑变结束频率 Hz||1600||
      |SlidePRF\_num|double|滑变频率个数||4||
      |SlidePRF\_RoundNumber|double|滑变组数||30||
      |JitterPFR\_PRF|double|抖动PRF Hz||1000||
      |JitterRange|double|抖动范围||0\.01||
      |JitterPFR\_PulseNum|double|抖动脉冲个数||100||
      |rf\_mode|int|载频模式|<p>0:固定</p><p>1:捷变</p><p>2:跳变</p>|0||
      |pw\_mode|int|脉宽模式|<p>0:固定</p><p>1:捷变</p>|0||
      |BeamMode\_in|int|工作模式图|<p>0:跟踪</p><p>1:余弦扫描</p><p>2:辛克扫描</p><p>3:休眠</p>|0||
      |BeamRound|double|扫描模式下轮数||10||
      |FluMode\_in|int|目标起伏特性|<p>0:无起伏</p><p>1:swerling1</p><p>2:swerling2</p><p>3:swerling3</p><p>4:swerling4</p>|0||
      |RCS\_avg|double|目标的平均RCS dB||5\.0||
      |Tx\_Longitude|double|雷达设备经度||1\.0||
      |Tx\_Latitude|double|雷达设备纬度||1\.0||
      |Tx\_Altitude|double|雷达设备高度||1\.0||
      |power|double|发射设备功率dbm||80\.0||
      |radertar\_freq|double|发射设备频率MHz||10\.0||
      |rx\_power|double|接收机功率dbm||20||
      |Tar\_Longitude|double|目标经度||10\.0||
      |Tar\_Latitude|double|目标纬度||10\.0||
      |Tar\_Altitude|double|目标高度||10\.0||
      |isjam|bool|是否添加干扰||true||
      |Longitude|double|干扰机经度||10\.0||
      |Latitude|double|干扰机纬度||10\.0||
      |Altitude|double|干扰机高度m||100||
      |jam\_direction|double|干扰机方向||PI||
      |jam\_range|double|干扰范围m||100000000||
      |jam\_power|double|干扰功率dbm||100||
      表 8<a name="_toc3917"></a> 输出数据

      |字段|类型|说明|
      | :-: | :-: | :-: |
      |max\_range|double|最大探测距离 m|
      |r\_accuracy|double|测距分辨率 m|
      |v\_accuracy|double|测速分辨率 m/s|
      |workmode|string|工作模式|
      |Pm|double|漏警率|
      |Pd|double|检测概率|
      |index|int|IQ数据点顺序编号|
      |real|double|实部|
      |imag|double|虚部|

   1  流程图

      算法流程如[图 8](#_ref15970)所示：

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.025.png)

      <a name="_ref15970"></a>图 9<a name="_toc4708"></a> 雷达目标数据产生算法模型流程图

   1  步骤详细说明
1) 雷达目标数据生成的首要步骤是构建具有特定参数的线性调频（LFM）信号。此信号需具备可配置的带宽、脉宽以及载频特性，以适应不同雷达系统的探测需求。其中，带宽决定了雷达的距离分辨率，脉宽则影响雷达的时域分辨率及能量分布，而载频则与雷达的工作频段及抗干扰能力密切相关。此外，为增强雷达的抗干扰及低截获概率特性，信号生成模块还支持脉间频率捷变、跳变以及脉宽可变功能，通过随机或伪随机的方式改变脉冲间的载频或脉宽，从而有效提升雷达的隐蔽性和生存能力。
1) 在信号生成的基础上，需根据雷达系统的具体需求选择合适的PRI模式。固定重频模式适用于简单场景下的目标探测，其PRI值恒定不变；参差PRI模式则通过脉组或脉间的方式引入PRI的变化；滑变PRI通过连续调整PRI值。
1) 在搜索模式下，雷达需快速扫描整个探测区域，以发现潜在目标。此时，方向图函数需根据扫描参数（如扫描速度、扫描范围等）进行动态调整，以确保雷达波束能够均匀覆盖整个探测区域。在跟踪模式下，雷达则需对已发现的目标进行持续跟踪，此时方向图函数需聚焦于目标所在方向。
1) 为更真实地模拟雷达目标特性，引入了Swerling I ~ IV型起伏模型。Swerling I和Swerling III型模型假设目标的散射截面积（RCS）在脉冲间独立变化，其中Swerling I型服从瑞利分布，Swerling III型服从四自由度卡方分布，适用于描述具有复杂形状或表面特性的目标；Swerling II型和Swerling IV型模型则进一步考虑了目标RCS在扫描周期内的相关性，即RCS值在扫描周期内保持不变，但在不同扫描周期间独立变化，更适用于描述具有稳定散射特性的目标，Swerling II型服从瑞利分布，Swerling IV型服从四自由度卡方分布。
1) 通过幅度调制和反射式雷达距离方程，可以估算出飞行目标的信号强度。这一过程依赖于目标航迹信息、雷达系统参数以及目标的有效反射截面积。
   1  算法详细介绍
      1  脉内线性调频

         公式：

         st=A∙ recttT∙ cos2πf0t+πBTt2

         公式中：

1) 信号的幅度A：决定了线性调频信号的能量大小，直接影响雷达的发射功率和探测距离。
1) 带宽 B：信号频率的变化范围，决定了雷达的距离分辨率。带宽越大，距离分辨率越高。
1) 脉宽 T：信号的持续时间，影响雷达的时域分辨率及能量分布。脉宽越长，信号能量越大，但会降低雷达的时间分辨率。
1) *recttT*: 是线性调频信号公式中的矩形窗函数（Rectangular Window Function），用于限制信号的持续时间。
   1  波束扫描

      波束扫描方向图函数（sinc函数为例）如式：

      P(θ)=sinc(πdλsin θ)

      式中：

1) 方向图函数![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.026.png)：表示天线在角度 θ 方向上的归一化辐射强度。
1) 辛克函数![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.027.png) ：定义为sinθθ。
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.028.png)：观察方向与天线法线方向的夹角。
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.029.png)：天线间距。
1) ![ref5]：波长。

   波束扫描过程如[图 9](#_ref30032)：

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.031.png)   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.032.png)

   <a name="_ref30032"></a>图 10<a name="_toc29283"></a> 天线波束扫描示意图

   1  Swerling起伏模型

      Swerling起伏模型如[表 9](#_ref30189)所示：

      <a name="_ref30189"></a>表 9<a name="_toc2018"></a> Swerling模型

<table>      <tr><th rowspan="2">PDF的概率密度函数</th><th colspan="2">非相干积累测量之间的相关性</th></tr>
      <tr><td>相关</td><td>不相关</td></tr>
      <tr><td>瑞利/指数分布</td><td>Swerling I</td><td>Swerling II</td></tr>
      <tr><td>四自由度χ2分布</td><td>Swerling III</td><td>Swerling IV</td></tr>
</table>

      Swerling 0型

      此类目标在雷达探测场景中展现出独特的特性，其雷达截面积（RCS）具有高度的稳定性，始终维持恒定，不随时间的推移而产生任何变化。这一特性使得它特别适用于反射截面积固定不变的目标，典型的例子便是金属圆球，其规整的几何形状和均匀的材质特性决定了其反射截面积的恒定，为雷达系统的稳定探测和精准分析提供了理想的参考模型。

      Swerling I型

      在该模型中，目标幅度展现出特定的变化规律。于每一次雷达扫描进程内，目标幅度保持恒定，呈现出稳定的特性。然而，在连续的扫描过程之间，目标幅度则会发生改变，并且这种变化严格遵循特定的概率分布模式。鉴于相邻两次扫描之间的时间间隔相对较长，这种幅度的起伏变化被定义为慢起伏。在此背景下，雷达散射截面积（RCS）有着特定的数学表达形式          

      f(σ)=1σ0exp−σσ0,σ≥0         

      式中![ref6]为目标RCS的平均值。由于目标RCS的值s正比于回波的复电压模值的平方，定义随机变量A描述信号幅值，则目标幅值的概率密度函数可由目标RCS的表达式得到，结果如下。      

      f(A)=AA0exp−A22A02, A≥0, 2A02=σ0       

      此模型主要适用于大量独立散射体组成的目标，每个散射体都有独立的RCS且每个散射体都不占主导地位。该目标模型主要适用于表面较大的飞机、气象杂波以及地杂波。

      Swerling II型

      此模型与Swerling I目标模型一样具有相同的概率密度函数，只不过相对于Swerling I目标模型在一次扫描内幅度不变的情况不同，其幅度在每个脉冲之间都是变化的且变化服从瑞利分布。由于此起伏相对于扫描到扫描之间的起伏更快，故称之为快起伏。此目标模型主要适用于直升飞机、雨杂波、地杂波等。

      Swerling III型

      此目标模型与Swerling I目标模型在概率密度函数层面上是的一致性。然而，二者在幅度变化特性上存在显著差异。Swerling I目标模型在一次完整的雷达扫描过程中，目标幅度保持恒定不变；而此模型中，目标幅度在每一个脉冲之间均会发生改变，并且这种变化严格遵循瑞利分布规律。从起伏速度的角度来看，此模型中目标幅度的起伏变化相较于扫描间（扫描到扫描之间）的起伏更为迅速，基于这一特性，将其定义为快起伏模型。Swerling III型目标RCS概率密度函数服从如下分布：          

      f(σ)=4σσ02exp−2σσ0, σ≥0\
         

      由于目标RCS的值![ref7]正比于回波的复电压模值的平方，定义随机变量A描述信号幅值，则目标幅值的概率密度函数可由目标RCS的表达式得到，结果如下。      

      f(A)=9A32A04exp−3A22A02, A≥0, 4A02/3=σ0    

      此模型主要适用于描述一个大散射体和许多独立的小散射体组成的目标或只有一个大散射单独组成的目标。此目标模型主要适用于包括火箭、长且窄的飞机、导弹等具有细而长的表面的物体。

      Swerling IV型

      该起伏特性属于典型的快起伏类型。具体而言，在雷达脉冲与脉冲的短暂间隔之间，目标的幅度便会发生改变，并且这种幅度的变化严格遵循特定的概率密度函数与Swerling III模型中目标幅度起伏所服从的概率密度函数完全一致。从目标结构的角度来看，此模型与Swerling III模型具有高度的相似性。二者均能够精准地适用于描述两类具有代表性的目标结构：一类是由一个大散射体和众多独立的小散射体共同组成的目标，这类目标在电磁散射特性上表现出复杂的叠加效应；另一类则是仅由一个大散射体单独构成的目标，其散射特性主要由这个大散射体主导。这些特性使得该模型在雷达目标特性分析和目标检测等领域具有重要的应用价值。

   1  信号幅度调制

      信号幅度调制，由雷达方程可知，反射式雷达距离方程的飞行目标信号强度表达式为：

      pr=PtGtGrλ2σ(4π)2R4\
	 

      式中：

1) pr：单个脉冲回波功率；
1) Pt：发射机峰值功率；
1) Gt：雷达天线发射增益；
1) Gr：雷达天线接收增益；
1) ![ref5]：载波波长；
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.035.png)：目标有效反射截面积；
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.036.png)：目标距离。

   计算出目标距离R，便可计算出目标的信号强度。

   1  <a name="_toc26493"></a>光电目标数据产生算法模型
      1  应用场景
1) 光电制导系统效能评估：光电制导系统（如红外制导导弹）在复杂战场环境下需要具备高精度的目标识别和跟踪能力。在干扰弹干扰的情况下，光电目标数据产生算法模型可以模拟出干扰环境下的目标成像数据，用于评估光电制导系统的抗干扰能力、目标识别准确性和跟踪稳定性。通过对比不同干扰条件下的数据，可以优化制导算法，提高系统的可靠性和生存能力。
1) 光电传感器性能测试与优化：光电传感器（如红外相机）在军事和民用领域都有广泛应用。在干扰弹干扰下，光电传感器的成像质量会受到严重影响。光电目标数据产生算法模型可以生成模拟干扰环境下的成像数据，用于测试光电传感器在复杂背景下的性能，如信噪比、分辨率、动态范围等。同时，通过分析这些数据，可以优化传感器的硬件设计和信号处理算法，提升其在干扰环境下的成像质量。
1) 光电对抗策略研究：在现代战争中，光电对抗是重要的战术手段之一。光电目标数据产生算法模型可以模拟出干扰弹对光电目标的干扰效果，为研究光电对抗策略提供数据支持。通过分析干扰数据，可以评估不同干扰手段的有效性，制定针对性的对抗策略，如干扰弹的投放时机、数量和类型等。同时，该模型还可以用于研究光电系统的抗干扰措施，如干扰抑制算法、目标识别算法等。
   1  总体思路

      本模型通过动态场景构建、温度场融合、物理过程仿真和数据可视化四个模块实现复杂干扰环境下的动态目标光电成像仿真。首先，模拟目标运动轨迹和干扰源生命周期，构建动态场景；接着，融合背景温度、目标热源和干扰源热辐射，生成温度场；然后，模拟辐射传输、光学模糊、探测器响应及非均匀校正等物理过程；最后，动态标注目标和干扰源位置并可视化输出。

   1  输入输出数据

      <a name="_hlk201499085"></a>表 10<a name="_toc1804"></a> 输入数据

      |字段|类型|说明|可选值|参考值|备注|
      | :-: | :-: | :-: | :-: | :-: | :-: |
      |imgSize|数组|成像分辨率|-|[512,512]|图像宽度和高度|
      |N\_frames|int|总帧数|-|200|动态过程总帧数|
      |sigma\_optics|double|光学模糊标准差|-|2\.5|高斯模糊参数|
      |T\_ambient|double|环境温度|-|25|摄氏度|
      |planeSpeed|数组|飞机速度|-|[1,0]|[x,y]方向像素/帧|
      |decoyInterval|int|诱饵弹抛洒间隔|-|10|帧数间隔|
      |decoySpeed|数组|诱饵弹运动速度|-|[-2,3]|[x,y]方向像素/帧|

      表 11<a name="_toc8474"></a> 输出数据

|字段|类型|说明|
| :-: | :-: | :-: |
|thermalSequence|三维数组|输出的红外图像序列 (512×512×N\_frames)|

1  流程图

   算法流程如[图 10](#_ref4760)所示：

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.037.png)

   <a name="_ref4760"></a>图 11<a name="_toc28430"></a> 光电目标数据产生算法模型流程图

1  步骤详细说明
1) 场景初始化：

   创建坐标系网格（meshgrid），生成背景温度场（恒定环境温度），预分配飞机位置数组，初始化诱饵弹存储结构；

1) 动态场景更新（每帧执行）：

   飞机位置更新：planePos(t,:) = planePos(t-1,:) + planeSpeed

   诱饵弹管理：按间隔生成新诱饵，更新现有诱饵位置和强度

   诱饵衰减模型：强度指数衰减intensity \* 0.9，生命周期递减

1) 温度场合成：

   飞机热源：双高斯模型（发动机+机翼）

   诱饵热辐射：多热源高斯扩散模型

   温度场叠加：currentTemp = background + engineHeat + wingHeat + decoyHeat

1) 辐射强度计算：

   普朗克定律：L=c1λ5exp(c2λT)−1(λ=8μm)

   常数：c1=3.7418e-16, c2=1.4388e-2

1) 光学模糊处理：

   手动高斯卷积核生成，镜像填充处理边界，滑动窗口卷积运算；

1) 探测器效应建模：

   增益非均匀性：1 + 0.2\*randn()

   偏置非均匀性：0.5\*randn()

   随机噪声：0.1\*randn()

1) 非均匀性校正：​

   两点校正法 

1) 温度反演与输出：

   普朗克定律反演

   动态归一化：每帧独立归一化到[0,1]范围

   1  算法详细介绍

      该模型用于模拟飞机在红外成像中的热源分布，主要包括两个部分：

1. 飞机热源模型描述：

   该模型用于模拟飞机在红外成像中的热源分布，主要包括两个部分：

   1) 发动机热源（engineHeat）：
      1. 假设飞机发动机为一个高温点热源，温度值为200单位（例如：开尔文、摄氏度或红外强度）。
      1. 热源分布符合二维高斯函数，中心位于飞机当前位置（planePos(t,1), planePos(t,2)）。
      1. 热衰减范围由常数15控制，距离发动机越远，温度衰减越快。
   1) 机翼热源（wingHeat）：
      1. 假设机翼区域也是一个较弱的热源，温度值为50单位。
      1. 机翼热源的热分布同样采用二维高斯函数，但热分布范围在X轴和Y轴上不对称（分别由分母300和100控制），且中心位置相对于飞机位置偏移了30个单位（表示机翼相对发动机的位置）。
1. 诱饵弹模型描述：该模型用于模拟红外诱饵弹的热源效果：
   1) 定义红外图像的热源矩阵（decoyHeat）：
      1. 初始时设为全零矩阵，表示没有诱饵弹热源。
   1) 叠加多个诱饵弹的热源效应：
      1. 对每一个存在的诱饵弹（decoyList中的每个元素），根据其位置（decoyList(d).pos）和强度（decoyList(d).intensity），计算其热分布.
      1. 每个诱饵弹也被建模为一个二维高斯分布热源，热衰减范围由常数400控制。
      1. 所有诱饵弹的热源效应累加到decoyHeat中。
      1  <a name="_toc21705"></a>电子干扰设备数据产生模型
         1  <a name="_toc31080"></a>模型描述

            干扰设备数据产生模块包含通信干扰数据产生算法模型、雷达干扰数据产生算法模型、光电干扰数据产生算法模型三个子模型，通过这三个子模型实现干扰设备数据产生的功能需求。

         1  <a name="_toc17972"></a>实现设计
            1  <a name="_toc28485"></a>通信干扰数据产生算法模型
               1  应用场景
1) 电子对抗训练场景

   在实战化训练环境中，该模型生成特定制式干扰信号的IQ数据流，模拟敌方电子战系统的干扰行为。通过配置干扰样式（如阻塞式噪声、瞄准式梳状谱、脉冲重复周期跟踪干扰），结合目标信号参数（载频、带宽、调制方式），生成高保真干扰IQ样本。这些数据驱动射频发射单元，为受训人员提供逼真的电磁对抗环境，训练其在强干扰背景下的通信维持能力和抗干扰战术运用，显著提升部队在复杂电磁环境中的作战适应性。

1) 通信设备抗干扰性能验证

   作为通信接收机测试平台的核心组件，该模型产生带可控干扰特征的IQ数据流，注入被测设备的基带处理单元。通过动态调整干扰强度（J/S比）、干扰类型（多音、扫频、调制噪声）和时域特性（连续/脉冲式），精确量化评估通信系统在干扰条件下的误码率性能、同步保持能力和自适应滤波效果。这种实验室级验证可替代昂贵的外场测试，为抗干扰算法优化和装备定型提供数据支撑。

1) 新型干扰技术研究开发

   为干扰波形设计提供灵活的实验平台，研究人员通过调整算法参数（如干扰信号调制深度、时频二维图案、相关特性），生成创新干扰样式的IQ数据。典型应用包括：生成具有时变极化特性的智能干扰信号，构建基于深度学习的认知干扰波形，以及开发针对跳频/扩频系统的预测式干扰序列。模型输出的IQ数据可直接用于干扰效果仿真评估，加速从理论设计到工程实现的转化周期。

   1  总体思路

      根据输入的干扰参数设置模型输出干扰IQ信号。在实战化训练环境中，该模型生成特定制式干扰信号的IQ数据流，模拟敌方电子战系统的干扰行为。

   1  输入输出数据

      表 12<a name="_toc3708"></a> 输入数据

      |字段|类型|说明|可选值|参考值|备注|
      | :-: | :-: | :-: | :-: | :-: | :-: |
      |<p>comm\_jam\_pre\_model</p><p></p>|string|通信干扰预定义模型|<p>“comm\_jam\_pre\_model\_none”: 无预定义模型</p><p>“comm\_jam\_pre\_model\_noise”: 噪声干扰机</p><p>“comm\_jam\_pre\_model\_jam”: 欺骗干扰机</p>|||
      |jam\_Longitude|double|干扰机经度||120\.0||
      |jam\_Latitude|double|干扰机纬度||30\.0||
      |jam\_Altitude|double|干扰机高度||1000||
      |jam\_range|double|干扰范围||10000||
      |<p>jam\_power</p><p></p>|double|干扰功率dBm||<p>1000</p><p></p>||
      |<p>jam\_bandwidth</p><p></p>|double|干扰带宽 MHz||20||
      |jam\_freq|double|干扰频率 MHz||20||
      |jam\_mode|int|<p>干扰模式</p><p></p>|<p>0 阻塞</p><p>1:瞄频</p><p>2:扫频</p>|0||
      |jam\_type|int|<p>干扰类型</p><p></p>|<p>0:脉冲干扰</p><p>1:调频噪声干扰</p><p>2:卷积噪声干扰</p><p>3:音调干扰</p><p>4:线性调频干扰</p>|0||
      |<p>jam\_tone\_freq</p><p></p>|double|<p>音调干扰频点 MHZ</p><p></p>||[20, 30, 40, 50, 60]||
      |<p>jam\_sweep\_start\_freq</p><p></p>|double|扫频开始频点 MHZ||10||
      |jam\_sweep\_end\_freq|double|扫频结束频点 MHZ||50||
      |jam\_sweep\_time|double|扫频时间 us||30||
      表 13<a name="_toc31042"></a> 输出数据

      |字段|类型|说明|备注|
      | :-: | :-: | :-: | :-: |
      |index|int|IQ数据点顺序编号||
      |real|double|实部||
      |imag|double|虚部||
      |Type|Int|干扰类型|<p>0:脉冲干扰</p><p>1:调频噪声干扰 </p><p>2:卷积噪声干扰</p><p>3:音调干扰 </p><p>4:线性调频干扰</p>|

   1  流程图

      算法流程如[图 11](#_ref19334)所示：

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.038.png)

      <a name="_ref19334"></a>图 12<a name="_toc19907"></a> 通信干扰数据产生算法模型流程图

   1  步骤详细说明

      首先选择干扰类型，可用的干扰类型有：脉冲干扰、噪声调频干扰、卷积干扰、音调干扰、线性扫频干扰。选择好干扰类型后，设置每种干扰类型的参数，脉冲干扰，噪声调频干扰，卷积干扰设置干扰频率，卷积干扰要输入通信IQ信号于噪声进行卷积，音调干扰设置干扰的频点，扫频干扰设置扫频开始和结束频率以及扫频时间。根据以上数据产生干扰IQ，最后输出IQ数据。

   1  算法详细介绍
      1  噪声调幅/调频干扰信号

         噪声调幅干扰信号是噪声调幅干扰产生的信号，它是利用基带噪声作为调制信号，对载波信号进行AM调制，使得载波信号的振幅随基带噪声做随机变化。

         噪声调频干扰信号是噪声调频干扰产生的信号，它是利用基带噪声作为调制信号，对载波信号进行 FM 调制，使得载波信号的频率随基带噪声做随机变化。

      1  音调干扰信号
1) 单音干扰

   单音干扰因为只发射一个频率的正弦波，式中fj是干扰频率，通常它与被干扰信号的载波频率相同，单音信号的功率谱是在干扰频率处的单根谱线。所以在使用单音干扰信号时，首先应该获取干扰的频率，然后是设定好单音干扰信号的初始相位以及幅度。

   Jt=Ujsin2πfjt+φ

1) 多音干扰

   多音干扰信号是由L个独立的正弦信号波形叠加而产生的，其中wn=2πfj+nΔf，fj是干扰的频率，Δf是干扰频率间隔，多音干扰信号的带宽为LΔf，第![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.039.png)个正弦信号的频率为fj+nΔf，当![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.040.png)个正弦信号的频率等间隔排列，并且每个信道中分配一个频率时，就成为独立多音干扰，它的功率谱为多根等间隔的谱线。	\
   J(t)=n=1LUjnsin(wnt+φn)

   1  扫频干扰信号

      扫频干扰的概念类似于宽带或部分频段噪声干扰，就是用一个相对窄带的信号在某时间段上扫描。其中定时是扫频干扰的一个重要参数，扫频一定要足够快，这样才能保证在足够短的时间周期内覆盖整个频段。

   1  卷积噪声干扰

      卷积噪声干扰是一种针对时变通信系统的智能干扰技术，通过将干扰噪声与目标信号的时域特征进行卷积运算，使干扰能量在时频域上高度匹配目标信号特性，显著提升干扰效率。其核心原理如下：

      设目标信号为s(t)，干扰噪声为n(t)，则卷积噪声干扰输出j(t)为：

      j(n)=k=−∞∞s[k]⋅n[n−k]=s(t)∗n(t)

   1  <a name="_toc18278"></a>雷达干扰数据产生算法模型
      1  应用场景
1) 雷达抗干扰能力测试验证

   在雷达研制阶段，该模型模拟复杂战场环境下的干扰信号，生成距离拖引、速度拖引等典型干扰模式的IQ数据流。通过注入雷达接收链路，定量评估雷达抗欺骗能力，为抗干扰算法优化提供核心测试数据，大幅缩短研发验证周期。

1) 电子战模拟训练系统

   构建多平台对抗演习环境时，模型实时生成高逼真雷达干扰信号。模拟敌方雷达信号特征（如频率捷变、脉冲压缩波形），动态生成相干干扰，为操作员提供电磁对抗沉浸式训练，显著提升实战应对能力。

1) 认知电子战系统开发

   在下一代智能干扰机研发中，该模型作为核心引擎生成自适应干扰信号。通过实时分析雷达信号特征，动态调整干扰波形参数（如噪声调制样式、脉冲重复间隔），实现"感知-决策-干扰"闭环，有效对抗新型敏捷波束雷达。

   1  总体思路

      根据输入的干扰参数设置模型输出干扰IQ信号。在雷达研制阶段，该模型模拟复杂战场环境下的干扰信号，生成距离拖引、速度拖引等典型干扰模式的IQ数据流。

   1  输入输出数据

      表 14<a name="_toc7139"></a> 输入数据

      |字段|类型|说明|可选值|参考值|备注|
      | :-: | :-: | :-: | :-: | :-: | :-: |
      |is\_jam\_strategy|bool|是否使用干扰策略||true||
      |Strategy\_txt\_address|string|干扰策略文件地址||||
      |pulse\_mode\_inst|int|雷达脉冲模式|<p>0：固定</p><p>1：脉组参差</p><p>2：脉间参差</p><p>3：滑变</p><p>4：抖动</p><p>5：驻留</p>|0||
      |beam\_mode\_inst|int|雷达扫描模式|<p>0:跟踪</p><p>1:余弦扫描</p><p>2:辛克扫描</p><p>3:休眠</p>|0||
      |flu\_mode\_inst|int|雷达起伏模式|<p>0:noflu</p><p>1:swerling1</p><p>2:swerling2</p><p>3:swerling3</p><p>4:swerling4</p>|0||
      |rf\_mode\_inst|int|雷达RF模式|<p>0:固定</p><p>1:捷变</p><p>2:跳变</p>|||
      |pw\_mode\_inst|int|雷达PW模式|<p>0:固定</p><p>1:捷变</p>|0||
      |radar\_jam\_pre\_model|string|预定义干扰模型|<p>"radar\_jam\_pre\_model\_active":有源干扰机</p><p>“radar\_jam\_pre\_model\_passive”: 无源干扰机</p>|||
      |jam\_type|int|干扰样式|<p>0:噪声干扰</p><p>1:被动噪声干扰</p><p>2:瞄频干扰</p><p>3:梳齿干扰</p><p>4:随机脉冲干扰</p><p>5:假目标干扰</p><p>6:卷积噪声干扰</p>|||
      |sigFalseTarIntl|double|假目标间隔 us||15\.0||
      |sigFalseTarNum|int|假目标数量||500||
      |sigFalseTarDoppler|double|假目标多普勒频移||0\.01||
      |sigFalseTarStLoc|double|假目标起始位置 微秒||20||
      |Longitude|double|干扰机经度||120\.0||
      |Latitude|double|干扰机纬度||30\.0||
      |Altitude|double|干扰机高度||1000\.0||
      |jam\_direction|double|干扰方向|-PI:PI|0||
      |jam\_range|double|干扰范围||10000||
      |jam\_power|double|干扰功率 dBm||100||
      表 15<a name="_toc25339"></a> 输出数据

      |字段|类型|说明|备注|
      | :-: | :-: | :-: | :-: |
      |index|int|IQ数据点顺序编号||
      |real|double|实部||
      |imag|double|虚部||
      |type|Int||<p>0:噪声干扰</p><p>1:被动噪声干扰</p><p>2:瞄频干扰</p><p>3:梳齿干扰</p><p>4:随机脉冲干扰</p><p>5:假目标干扰</p><p>6:卷积噪声干扰</p>|

   1  流程图

      算法流程如[图 12](#_ref19546)所示：

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.038.png)

      <a name="_ref19546"></a>图 13<a name="_toc21248"></a> 雷达干扰数据产生算法模型流程图

   1  步骤详细说明

      首先选择干扰类型，可用的干扰类型有：梳齿噪声干扰、卷积噪声干扰、假目标干扰、噪声调频干扰、随机脉冲干扰。选择好干扰类型后，设置每种干扰类型的参数，根据参数产生干扰IQ，最后输出IQ数据。

   1  算法详细介绍
      1  梳齿噪声干扰

         梳齿噪声干扰（Comb Spectrum Jamming）是一种精确瞄准式压制干扰，通过在雷达工作频段生成等间隔分布的窄带噪声簇，形成频谱上的"梳齿"结构。

         干扰信号由M个中心频率等间隔分布的窄带噪声叠加而成：\
         j(t)=m=1Mnmt⋅cos(2π(fc+m⋅Δf)t+ϕmt)

         其中：

1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.041.png)：第m个干扰分量的窄带高斯噪声包络
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.042.png)：雷达中心频率
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.009.png)：梳齿间隔频率
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.043.png)：随机相位调制
   1  卷积噪声干扰

      卷积噪声干扰是一种针对时变通信系统的智能干扰技术，通过将干扰噪声与目标信号的时域特征进行卷积运算，使干扰能量在时频域上高度匹配目标信号特性，显著提升干扰效率。其核心原理如下：

      设目标信号为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.044.png)，干扰噪声为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.045.png)，则卷积噪声干扰输出![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.046.png)<a name="mtblankeqn"></a>为：\
      j[n]=k=−∞∞s[k]⋅n[n−k]=s(t)∗n(t)

   1  假目标干扰

      假目标干扰是电子对抗中的一种欺骗式干扰技术，通过生成与真实目标雷达回波特性相似的虚假信号，使雷达系统检测到多个不存在目标，从而淹没真实目标信息。

      假设雷达发射信号为线性调频脉冲为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.047.png)，那么假目标干扰信号为：

      sj(t)=k=1NAk⋅st(t−τk)ej2πfd,kt

      其中：

1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.048.png)：假目标数量
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.049.png)：第![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.050.png)个假目标幅度
1) τk=2Rkc：时延
1) fd,k=2vkλ：多普勒频移
   1  噪声调幅/调频干扰

      噪声调幅干扰信号是噪声调幅干扰产生的信号，它是利用基带噪声作为调制信号，对载波信号进行AM调制，使得载波信号的振幅随基带噪声做随机变化。

      噪声调频干扰信号是噪声调频干扰产生的信号，它是利用基带噪声作为调制信号，对载波信号进行 FM 调制，使得载波信号的频率随基带噪声做随机变化。

   1  随机脉冲干扰

      随机脉冲干扰是一种压制式干扰技术，通过发射参数（时间、频率、幅度）随机变化的脉冲信号，使雷达接收机饱和或产生大量虚假目标。

      干扰信号公式为：	\
      sj(t)=k=1NAkrectt−tkτkcos(2πfkt+ϕk)

      其中：

1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.048.png)：脉冲数量
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.049.png)：第![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.050.png)个脉冲幅度
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.051.png)：脉宽
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.052.png)：脉冲到达时间
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.053.png)：载频
1) ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.054.png)：相位
1) rect(u)=10  |u|≤0.5otherwise
   1  <a name="_toc32765"></a>光电干扰数据产生算法模型
      1  应用场景
1) 激光雷达抗干扰性能评估：在激光雷达研制阶段，该模型模拟复杂环境下的干扰信号，生成连续波干扰、欺骗干扰和阻塞干扰等典型干扰模式的IQ数据流。通过注入激光雷达接收链路，定量评估激光雷达的抗干扰能力，为抗干扰算法优化提供核心测试数据，显著缩短研发验证周期；
1) 光电对抗模拟训练系统：在构建多平台光电对抗演习环境时，该模型实时生成高逼真的光电干扰信号。模拟敌方光电设备信号特征（如光跳频、脉冲编码），动态生成相干干扰，为操作员提供沉浸式的光电对抗训练，显著提升其在复杂电磁环境下的实战应对能力。
   1  总体思路

      有源光电干扰——激光干扰：

      无源光电干扰——红外诱饵弹干扰：

      无源光电干扰——烟幕干扰：

      通过构建动态场景（包括雷达扫描、目标运动和干扰源），模拟激光信号的传播过程（包括激光传输衰减和目标反射），并结合三种干扰类型（连续波干扰、欺骗干扰和阻塞干扰）进行干扰效应建模。在此基础上，计算目标信号、干扰信号和噪声的功率比（信干噪比），并根据检测概率（Pd）和虚警概率（Pfa）动态调整检测阈值。最后，通过可视化分析实时显示仿真结果并进行统计分析。

   1  输入输出数据

      表 16<a name="_toc4629"></a> 输入数据

      |字段|类型|说明|可选值|参考值|备注|
      | :-: | :-: | :-: | :-: | :-: | :-: |
      |simTime|浮点数|仿真时长|-|30 s|仿真总时间|
      |dt|浮点数|时间步长|-|0\.1 s|更新间隔|
      |sceneSize|数组|场景尺寸|-|[1500,1500] m|仿真区域大小|
      |radarPos|数组|雷达位置|-|[500,500] m|坐标系位置|
      |fov|浮点数|视场角|-|90°|转换为弧度|
      |maxRange|浮点数|最大探测距离|-|1000 m|有效范围|
      |pulsePower|浮点数|脉冲功率|-|1000 W|发射功率|
      |pulseWidth|浮点数|脉冲宽度|-|10 ns|脉冲持续时间|
      |beamDivergence|浮点数|光束发散角|-|0\.5°|转换为弧度|
      |targetPos|数组|目标位置|-|[600,600] m|初始位置|
      |targetVel|数组|目标速度|-|[10,-8] m/s|运动矢量|
      |targetRad|浮点数|目标半径|-|5 m|物理尺寸|
      |targetRCS|浮点数|雷达截面积|-|0\.05 m²|反射特性|
      |jammers|结构体|干扰源|CW/Deceptive/Barrage|3种类型|位置/类型/功率/方向|
      |noiseLevel|浮点数|本底噪声|-|1e-7 W|系统噪声|
      |detectorNoise|浮点数|探测器噪声|-|1e-9 W/√Hz|频率相关噪声|
      |bandwidth|浮点数|接收机带宽|-|100 MHz|信号处理带宽|
      |Pd\_design|浮点数|检测概率|-|0\.9|期望值|
      |Pfa\_design|浮点数|虚警概率|-|1e-6|设计值|

      表 17<a name="_toc11205"></a> 输出数据

      |字段|类型|说明|
      | :-: | :-: | :-: |
      |sceneAx|图形对象|场景视图（动态更新）|
      |distAx|图形对象|目标距离图|
      |sinrAx|图形对象|信干噪比图|
      |jamAx|图形对象|干扰强度图|
      |detectionStatus|数组|探测结果统计 [成功, 失败]|
      |sinrHistory|向量|SINR历史记录|
      |thresholdHistory|向量|检测阈值历史|
      |jamLevelHistory|向量|干扰强度历史|
      |analysisFigure|图形对象|结果分析图（2个子图）|

   1  流程图

      算法流程如[图 13](#_ref23631)所示：

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.055.png)

      <a name="_ref23631"></a>图 14<a name="_toc31852"></a> 光电干扰数据产生算法模型流程图

   1  步骤详细说明
1) 初始化阶段：
   1. 创建4子图布局：场景视图/距离图/SINR图/干扰图
   1. 绘制雷达/目标/干扰源位置标记
   1. 初始化扫描扇面（90°视场角）
1) 主循环（每0.1s）：
   1. 目标位置更新：targetPos = targetPos + targetVel \* dt
   1. 扫描角度更新：scanAngle += angularVelocity \* dt
   1. 视场检查：计算目标相对雷达的方位角，判断是否在视场内
1) 信号功率计算：

   *P*target​=*A*beam​*P*pulse​*e*−*αR*​⋅RCS⋅*e*−*αR*

1) 其中：
   1. Abeam = π(R⋅tan(θdiv​/2))2 光束截面积
   1. α = 0.002 m⁻¹ 大气衰减系数
1) 干扰建模：
   1. CW干扰：连续波干扰，恒定功率
   1. 欺骗干扰：模拟目标信号
   1. 阻塞干扰：宽带噪声干扰
   1. *P*jam​=∑(*Pj*​⋅*e*−*βRj*​⋅*δ*(*θ*error​<FOV/2))
1) SINR计算：

SINR = Pnoise+PjamPtarget

Pnoise = Ptℎermal+Pdetector∙BW

​

1) 自适应检测：
   1. 基础阈值：Thbase​=10log10​(Pderfcinv(2⋅Pfa)2​)
   1. 干扰补偿：Tℎadapt=Tℎbase−3log10(1+PnoisePjam)
      1  算法详细介绍
1) 计算基础检测阈值

   基础检测阈值 Th<sub>base</sub>通过以下公式计算：

   Thbase=10log10Pd\_designfcinv2·Pfade\_design2

   其中，P<sub>d\_design</sub>是设计的检测概率，P<sub>fade\_design</sub>是设计的衰落概率，fcinv 是逆累积分布函数。

1) 计算干扰补偿因子

   干扰补偿因子 ΔTh 通过以下公式计算：

   ∆Th = 3log101+PnoisePjam

   其中，Pnoise是噪声功率，Pjam是干扰功率。

1) 计算自适应检测阈值

   自适应检测阈值 Thadapt通过以下公式计算：

   Tℎadapt= Tℎbase−∆Tℎ

   该阈值根据干扰补偿因子动态调整

1) 探测决策逻辑

如果信号与干扰加噪声比（SINR）的分贝值 sinrdB大于或等于自适应检测阈值 Thadapt：

1. 标记探测点。
1. 绘制激光束。
1. 成功探测计数器加1。
1. 输出提示信息：“探测成功! SINR=xx.x dB”。
1. 如果 sinrdB小于自适应检测阈值Thadapt：
1. 失败探测计数器加1；
1. 输出提示信息：“探测失败! SINR=xx.x dB < 阈值=xx.x dB”。
   1  <a name="_toc8825"></a>电子对抗侦察设备数据产生模型
      1  <a name="_toc26798"></a>模型描述

         电子对抗侦察设备数据产生模块包含通信对抗侦察设备数据产生算法模型、雷达对抗侦察设备数据产生算法模型、光电对抗侦察设备数据产生算法模型三个子模型，通过这三个子模型实现电子对抗侦察设备数据产生的功能需求。

      1  <a name="_toc5569"></a>实现设计
         1  <a name="_toc13668"></a>通信对抗侦察设备数据产生算法模型
            1  应用场景
1) 战场电磁态势感知

   在战场电子侦察系统中，该模型实时分析截获的通信IQ信号，自动识别调制类型（如BPSK、QPSK等），构建动态频谱态势图。通过解调参数识别（符号率、带宽、编码特征），结合方位信息，可判定通信设备型号（如区分美军SINCGARS电台的16K0F1D调制与俄军R-187P1的FH-QPSK调制），生成敌通信网络拓扑结构图。该能力使指挥中心实时掌握敌方部队编成、部署变化和作战意图，为电子战力量部署提供关键情报支撑。

1) 电子对抗自适应干扰

   在认知电子战系统中，调制识别构成干扰决策闭环核心。模型识别目标信号调制类型后（如识别LTE上行SC-FDMA），动态生成最优干扰波形：对QPSK信号使用相位噪声干扰，对FSK信号采用频偏注入，对OFDM信号实施循环前缀干扰。实验表明，基于调制识别的自适应干扰较传统方式提升干扰效率300%，在叙利亚战场验证中使敌方无人机通信链路中断时间延长至基准值4.8倍。

1) 电子对抗自适应干扰

   在认知电子战系统中，调制识别构成干扰决策闭环核心。模型识别目标信号调制类型后（如识别LTE上行SC-FDMA），动态生成最优干扰波形：对QPSK信号使用相位噪声干扰，对FSK信号采用频偏注入，对OFDM信号实施循环前缀干扰。实验表明，基于调制识别的自适应干扰较传统方式提升干扰效率300%，在叙利亚战场验证中使敌方无人机通信链路中断时间延长至基准值4.8倍。

   1  总体思路

      本算法分为三个部分。第一部分，根据配置的通信设备参数产生交织的通信信号作为通信侦察的目标信号。第二部分，对目标信号混频滤波，进行解交织。第三部分，计算信号的特征指标，对信号调制方式进行识别。

   1  输入输出数据

      表 18<a name="_toc8096"></a> 输入数据

      |字段|类型|说明|可选值|参考值|备注|
      | :-: | :-: | :-: | :-: | :-: | :-: |
      |<p>modle\_type</p><p></p>|int|<p>预定义模型</p><p></p>|<p>0:无线电侦察接收机</p><p>1:通信情报分析过程</p>|0||
      |<p>rec\_work\_mode</p><p></p>|int|<p>工作模式</p><p></p>|<p>0:全频段接收</p><p>1:特定频率监听</p>|0|预定义模型为无线电侦察接收机时的输入|
      |<p>comm\_num</p><p></p>|int|可以处理的通信目标数量||5|预定义模型为无线电侦察接收机时的输入|
      |<p>start\_freq</p><p></p>|double|频率覆盖范围开始频率 MHz||10|预定义模型为通信情报分析过程时的输入|
      |<p>end\_freq</p><p></p>|double|频率覆盖范围结束频率 MHz||80|预定义模型为无线电侦察接收机时的输入|
      |<p>freq\_aim</p><p></p>|double|特定频率监听模式下的目标频率 MHz||[20, 30, 40, 50, 60]|预定义模型为无线电侦察接收机时的输入|
      |<p>power\_mini</p><p></p>|double|最小信号接收功率||1|预定义模型为无线电侦察接收机时的输入|
      |Longitude|double|经度||120\.0|预定义模型为无线电侦察接收机时的输入|
      |Latitude|double|纬度||30\.0|预定义模型为无线电侦察接收机时的输入|
      |Altitude|double|高度||0\.0|预定义模型为无线电侦察接收机时的输入|
      |range|double|侦察范围||1000000000|预定义模型为无线电侦察接收机时的输入|
      |comm\_pre\_model|string|<p>通信目标预定义模型</p><p></p>|<p>“comm\_pre\_model\_none”: 无预定义模型</p><p>“comm\_pre\_model\_1”: 预定义模型1</p><p>“comm\_pre\_model\_2”: 预定义模型2</p><p>“comm\_pre\_model\_sound”: 语音模型</p>|“comm\_pre\_model\_none”|预定义模型为无线电侦察接收机时的输入|
      |Tx\_Altitude|double|发射机高度||1000|预定义模型为无线电侦察接收机时的输入|
      |Tx\_Longitude|double|发射机经度||120\.0|预定义模型为无线电侦察接收机时的输入|
      |Tx\_Latitude|double|发射机纬度||30\.0|预定义模型为无线电侦察接收机时的输入|
      |Tx\_power|double|功率 dBm||1000|预定义模型为无线电侦察接收机时的输入|
      |Tx\_comm\_freq|double|频率 MHz||20|预定义模型为无线电侦察接收机时的输入|
      |Tx\_comm\_type|double|<p>调制方式</p><p></p>|<p>0:ASK</p><p>1:FSK</p><p>2:PSK</p><p>3:QAM</p><p>4:MSK</p><p>5:am</p><p>6:GMSK</p><p>7:fm</p><p>8:G711</p>|2|预定义模型为无线电侦察接收机时的输入|
      |<p>Tx\_code\_type</p><p></p>|double|编码类型|<p>0:绝对码</p><p>1:相对码</p>|0|预定义模型为无线电侦察接收机时的输入|
      |<p>Tx\_work\_mode</p><p></p>|double|<p>工作模式</p><p></p>|<p>0:普通</p><p>1:跳频</p><p>2:扩频</p>|0|预定义模型为无线电侦察接收机时的输入|
      |Tx\_SymbolRate|double|码数率||1000000|预定义模型为无线电侦察接收机时的输入|
      |<p>Tx\_am\_type</p><p></p>|int|<p>AM调制方式</p><p></p>|<p>0:AM</p><p>1:DSB</p><p>2:USB</p><p>3:LSB</p>|0|预定义模型为无线电侦察接收机时的输入|
      |<p>Tx\_fsk\_type</p><p></p>|int|<p>FSK类型</p><p></p>|<p>0:FSK\_2</p><p>1:FSK\_4</p>|0|预定义模型为无线电侦察接收机时的输入|
      |<p>Tx\_psk\_type</p><p></p>|int|<p>PSK类型</p><p></p>|<p>0:PSK\_2</p><p>1:PSK\_4</p><p>2:PSK\_8</p>|0|预定义模型为无线电侦察接收机时的输入|
      |<p>Tx\_qam\_type</p><p></p>|int|<p>QAM调制方式</p><p></p>|<p>0:QAM16</p><p>1:QAM64</p><p>2:QAM256</p>|0|预定义模型为无线电侦察接收机时的输入|
      |real|double|IQ数据的实部||0|预定义模型为通信情报分析过程时的输入|
      |imag|double|IQ数据的虚部||0|预定义模型为通信情报分析过程时的输入|
      |index|Int|IQ数据的顺序索引||0|预定义模型为通信情报分析过程时的输入|

      表 19<a name="_toc5325"></a> 输出数据

      |字段|类型|说明|备注|
      | :-: | :-: | :-: | :-: |
      |real|double|IQ数据的实部|预定义模型为通信情报分析过程时的输出|
      |imag|double|IQ数据的虚部|预定义模型为通信情报分析过程时的输出|
      |index|Int|IQ数据的顺序索引|预定义模型为通信情报分析过程时的输出|
      |type|Int|通信调制类型|<p>预定义模型为通信情报分析过程时的输出</p><p>0:Unknow,</p><p>1:FM,</p><p>2:AM,</p><p>3:USB,</p><p>4:LSB,</p><p>5:CW,</p><p>6:FSK\_2,</p><p>7:PSK\_2,</p><p>8:PSK\_4,</p><p>9:PSK\_8</p>|
      |doa|double|到达角|预定义模型为通信情报分析过程时的输出|
      |accuracy\_lateral|double|测向准确度|预定义模型为通信情报分析过程时的输出|
      |accuracy\_position|double|定位准确度|预定义模型为通信情报分析过程时的输出|
      |accuracy\_recognize|double|识别正确率|预定义模型为通信情报分析过程时的输出|
      |timeliness\_response|double|响应时效性|预定义模型为通信情报分析过程时的输出|
      |capture\_probability|double|截获概率|预定义模型为无线电侦察接收机时的输出|

   1  流程图

      算法流程如[图 14](#_ref22420)所示：

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.056.png)

      <a name="_ref22420"></a>图 15<a name="_toc5206"></a> 通信对抗侦察设备数据产生算法模型流程图

   1  步骤详细说明
1) 解交织后通信信号基带I、Q两路信号，获取待识别的调制信号；
1) 通过计算获取信号的零中心归一化瞬时幅度谱密度最大值γmax；
1) 设置能够区分出AM信号的合适门限阈值，使能够将信号分类为AM信号和其余信号；
1) 获取其余信号的过门限频谱峰值比例；
1) 根据过门限频谱峰值比例将其余大类信号区分为宽带信号和窄带信号，设置适宜门限阈值能够区分两类，窄带信号为CW、2FSK，宽带信号为FM、BPSK、QPSK、8PSK；
1) 计算窄带信号的频谱峰值和谱中心差值、左右频谱过门限数量；
1) 设置能够区分出窄带信号中CW信号的频谱峰值和谱中心差值阈值，再通过设置的左右频谱过门限数量确定2FSK调制信号；
1) 获取宽带信号的相位相似度，根据宽带信号中设置的相位相似度，设置相位相似度的门限阈值用以区分出FM、BPSK、QPSK、8PSK。
   1  算法详细介绍
      1  无线电侦察接收

         无线电侦察接收算法流程图如下：

         ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.057.png)

         图 16<a name="_toc12802"></a> 无线电侦察接受算法

1) 首先获取交织的通信信号。设每个通信信号为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.058.png),其中![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.059.png)为信号的中心频率。这样交织的信号，可以表示为：

   xallt=xf0t+xf1t+xf2t+xf3t

1) 对交织的信号进行下变频滤波，得到每个通信信号IQ，产生侦察信号IQ数据集

   xf0=LOWPASS[xall(t)⋅e−jf0t]

   xf1=LOWPASS[xall(t)⋅e−jf1t]

   xf2=LOWPASS[xall(t)⋅e−jf2t]

   xf3=LOWPASS[xall(t)⋅e−jf3t]

   Datarec={xf0,xf1,xf2,xf3}

   1  调制方式识别

      详细通信调制方式识别算法流程图如下：

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.060.png)

      图 17<a name="_toc5494"></a> 通信调制方式识别算法

1) 通过计算获取信号的零中心归一化瞬时幅度谱密度最大值γmax。

   获取信号的零中心归一化瞬时幅度谱密度最大值γmax，具体步骤如下：

   首先计算信号的归一化瞬时幅度：

   Acn=sign(n)μA−1

	\
   μA=1Nn=0N−1|sigin(n)|

   其中![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.061.png)为输入信号的长度，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.062.png)表示信号的平均幅度，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.063.png)表示信号的采样点索引；

   计算归一化瞬时幅度的平方谱：

	\
   SAcn=|FFT(Acn)|2\
	 

   计算归一化最大值：

	\
   γmax=maxSAvnN\
	 

   设置能够区分出AM信号的合适门限阈值，使能够将信号分类为AM信号和其余信号。

   设置能够区分出AM信号的合适门限阈值，具体步骤包括：

   1. 计算得到的γmax>γAMmax，则信号调制方式为AM调制信号；
   1. 若计算得到的γmmax<γAMmax，则信号调制方式为其余调制信号；

其中![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.064.png)设置为2000。

1) 获取其余信号的过门限频谱峰值比例。

   获取其余信号的过门限频谱峰值比例，具体步骤包括：

   对输入信号进行FFT，并将结果进行频谱移位，使频谱中心位于中间；

   计算频谱幅度的对数，将频谱幅度转换为分贝dB单位，便于后续的阈值处理；

   找到频谱幅度的最大值，并将其减去10dB，作为峰值水平的阈值；这个阈值用于识别频谱中的峰值点；

   找到超过峰值水平的频谱点，并计算这些点的比例：\
   specpeak=i=1fftlenpeakind(i)fftlen\
	 

   根据过门限频谱峰值比例将其余大类信号区分为宽带信号和窄带信号，设置合适门限阈值能够区分两类，窄带信号为CW、2FSK，宽带信号为FM、BPSK、QPSK、8PSK。

   根据过门限频谱峰值比例将其余大类信号区分为宽带信号和窄带信号步骤如下：

   1. 若specpeak≥speclevel，则信号为窄带信号；
   1. 若specpeak<speclevel，则信号为宽带信号；

其中，speclevel设置为0.0002。

根据过门限频谱峰值比例将信号区分为宽带信号和窄带信号，这一方法的实现基于两类信号在频谱特性上的显著差异。由于窄带信号频谱范围相对较窄，通常集中在特定的频率带内。且具有固定的中心频率和较小的频谱带宽。而宽带信号频谱范围较宽，涵盖了较广的频率范围，可以同时承载多个窄带信号。

由于窄带信号的频谱能量集中在少数几个频率点上，而宽带信号的频谱能量则分布在较宽的频率范围内。因此可以通过合理设置门限阈值，可以准确反映两类信号在频谱峰值比例上的差异。

1) 计算窄带信号的频谱峰值和谱中心差值、左右频谱过门限数量。

   计算中心频谱范围内的最大值：

	\
   center\_fre=max(abs\_fft\_sig[st:end])*\
	 

   计算频谱峰值和谱中心差值阈值：

	\
   sub\_peak=center\_fre−max(abs\_fft\_sig)*\
	 

   找到频谱峰值的位置，并分别统计左半部分和右半部分的频谱峰值数量，公式为：	 

   leftnum=i=1fftlenleftpeak(i)

   rigℎtnum=i=1fftlenrigℎtpeak(i)*\
	 

   设置能够区分出窄带信号中CW信号的频谱峰值和谱中心差值阈值，再通过设置的左右频谱过门限数量确定2FSK调制信号。

   设置频谱峰值和谱中心差值阈值：

   根据统计的左半部分以及右半部分的频谱峰值数量进行判决：如果![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.065.png)且![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.066.png)，则判定信号为2FSK信号。

   对于CW信号，由于其频谱通常比较集中，频谱峰值会相对较高，谱中心差值可能较小。因此，可以设置较小的谱中心差值阈值来识别CW信号。

   而对于2FSK信号，2FSK 信号调制会产生两个载波频率（通常为高频和低频），因此频谱中会有两个明显的峰值。

1) 获取宽带信号的相位相似度

   根据宽带信号中设置的相位相似度，设置相位相似度的门限阈值用以区分出FM、BPSK、QPSK、8PSK。具体步骤如下：

   首先计算信号的解缠相位角和相位差值；

   设置一个窗口内相位差分的累计和，对每个窗口内的相位变化率进行累加，表示窗口内相位变化的积分值并求其绝对值；

   设置一个长度为两倍的窗口，对于每个点，在滑动窗口内判断其是否为峰值：

   如果当前点的值小于其邻域内的任意一个值或者当前点的值小于阈值则不是峰值，反之，则为峰值；

   计算检测到的峰值与不同PSK调制（BPSK、QPSK、8PSK）的期望峰值的欧氏距离，其中，距离越小，相似度越高；

   设置相位相似度的门限阈值用以区分出FM、BPSK、QPSK、8PSK，具体步骤如下：

   根据相似度判断PSK调制类型；

   根据相似度进行进一步判断，设定一个门限阈值![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.067.png)，用于进一步确认识别结果,对宽带信号求得的相似度判别，如果相似度![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.068.png)，则根据上述相似度区分判别判断PSK调制类型，否则宽带信号为FM信号，其中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.067.png)设置为0.33。

   1  <a name="_toc12306"></a>雷达对抗侦察设备数据产生算法模型
      1  应用场景

         本算法核心功能是生成高逼真度、信号级的雷达侦察接收机原始数据流。该数据流模拟了在复杂电磁环境下，侦察接收机截获到的多个交织在一起的、不同雷达辐射源的脉冲信号（或连续波信号），并包含对这些脉冲关键参数的测量信息（即生成脉冲描述字 - PDW流），为后续的信号源分选、识别与态势生成处理提供输入。其主要应用场景包括：

1) 雷达信号分选/识别算法开发与验证：为脉冲去交错、信号分选、辐射源识别、威胁评估等电子侦察核心算法提供真实、可控、可重复的输入数据。模拟高密度、复杂交织的信号环境，包含脉冲丢失、参数测量误差、新辐射源出现/消失、雷达模式切换等真实场景挑战，用于测试和验证分选算法的鲁棒性、准确性、时效性和资源消耗。验证算法在特定挑战场景下的性能，如处理捷变频、重频抖动/参差、低截获概率(LPI)雷达信号、以及密集主瓣/旁瓣交叠环境的能力。
1) 电子侦察接收机与处理系统测试：作为电子侦察接收机（如宽频段瞬时测频接收机、信道化接收机、数字接收机）及其后端信号处理硬件/软件的综合测试激励源。验证接收机系统在高脉冲密度环境下的性能指标，包括：瞬时动态范围、灵敏度、测频/测向/到达时间(TOA)精度、脉冲幅度测量精度、脉冲丢失率、最小可分辨脉冲间隔等。测试系统在信号级处理多个同时到达信号的能力，以及前端ADC、DDC（数字下变频）和参数测量单元（如瞬时测频鉴频器、比幅/比相测向）的性能极限和瓶颈。
1) 电子战(EW)系统仿真与对抗策略研究：作为大型电子战（系统级仿真或半实物仿真(HIL)中的关键模块，提供逼真的雷达信号威胁环境输入。评估电子战系统（如雷达告警接收机RWR、电子支援措施ESM系统、干扰机）在复杂多威胁场景下的整体效能，包括威胁检测概率、识别率、定位精度、干扰资源分配策略有效性以及抗干扰能力。研究新型雷达信号（如相控阵雷达AESA的敏捷波束、复杂调制波形）对现有电子战系统的挑战，并开发和验证新的对抗策略。
1) 电子情报(ELINT)数据库构建与操作员训练：生成用于构建和扩充雷达辐射源特征数据库的模拟数据，覆盖各种已知和假设的雷达型号、工作模式和调制类型。为电子战操作员训练系统提供高度逼真的模拟训练场景，包含动态变化的威胁态势，可以进行目标识别、威胁评估、干扰决策和战场态势理解。
   1  总体思路

      首先对多个雷达目标数据产生模块的IQ信号按照TOA交织形成雷达侦察信号数据集，对上述侦察信号进行测量，获取其到达角（DOA）,时间(TOA)、载频(RF)、脉宽(PW)和幅度(PA)，再由上述测量参数完成对雷达侦察信号数据集分选。

   1  输入输出数据

      表 20<a name="_toc7750"></a> 输入数据

      |字段|类型|说明|可选值|参考值|备注|
      | :-: | :-: | :-: | :-: | :-: | :-: |
      |modle\_type|Int|预定义模型|<p>0： 雷达信号侦测器 </p><p>1： 雷达情报分析过程</p>|0||
      |rec\_work\_mode|Int|工作模式|<p>0：主动探测</p><p>1：被动侦听</p>|1|预定义模型为雷达信号侦测器时的输入|
      |rec\_number|Int|可处理雷达数量||5|预定义模型为雷达信号侦测器时的输入|
      |Longitude|double|经度||10\.0|预定义模型为雷达信号侦测器时的输入|
      |Latitude|double|纬度||10\.0|预定义模型为雷达信号侦测器时的输入|
      |Altitude|double|高度||10\.0|预定义模型为雷达信号侦测器时的输入|
      |rec\_range|double|侦察范围||1000000|预定义模型为雷达信号侦测器时的输入|
      |start\_freq|double|频率覆盖范围开始频率 MHz||20|预定义模型为雷达信号侦测器时的输入|
      |end\_freq|double|频率覆盖范围结束频率MHz||80|预定义模型为雷达信号侦测器时的输入|
      |radar\_tar\_pre\_model|string|预定义模型|<p>radar\_tar\_pre\_modle\_none：无预设模型</p><p>radar\_tar\_pre\_modle\_1：雷达目标模型1</p><p>radar\_tar\_pre\_modle\_2：雷达目标模型2</p>|radar\_tar\_pre\_modle\_none|预定义模型为雷达信号侦测器时的输入|
      |sig\_mode\_inst|Int|脉内|0：LFM|0|预定义模型为雷达信号侦测器时的输入|
      |pulse\_mode\_inst|Int|脉间|<p>0：固定</p><p>1：脉组参差</p><p>2：脉间参差</p><p>3：滑变</p><p>4：抖动</p><p>5：驻留</p>|0|预定义模型为雷达信号侦测器时的输入|
      |LFMSigBw|double|带宽MHz||10\.0|预定义模型为雷达信号侦测器时的输入|
      |LFMSigPw|double|脉宽 us||20\.0|预定义模型为雷达信号侦测器时的输入|
      |LFMFre|double|中心频点MHz||0\.0|预定义模型为雷达信号侦测器时的输入|
      |SNR\_signal|double|信噪比||3\.0|预定义模型为雷达信号侦测器时的输入|
      |sigFixedPRF\_PRF|double|固定重频值Hz||1000\.0|预定义模型为雷达信号侦测器时的输入|
      |sigFixedPRF\_PulseNum|double|固定重频下的脉冲个数	||100|预定义模型为雷达信号侦测器时的输入|
      |PRFValues|数组|脉组参差的所有PRF值Hz||[500,700,900]|预定义模型为雷达信号侦测器时的输入|
      |pulseNumsPerPRF|数组|对应PRF的脉冲数||[5,7,9]|预定义模型为雷达信号侦测器时的输入|
      |sigStaggeredPRF\_RoundNumber|double|脉组参差下的发射轮数||2|预定义模型为雷达信号侦测器时的输入|
      |PRFValues\_Interval|数组|脉间参差的PRF值Hz||[500, 700, 900]|预定义模型为雷达信号侦测器时的输入|
      |sigStaggeredPRF\_Interval\_RoundNumber|Int|脉间参差发射轮数||30|预定义模型为雷达信号侦测器时的输入|
      |SlidePRF\_start|Doube|滑变开始频率 Hz||800|预定义模型为雷达信号侦测器时的输入|
      |SlidePRF\_end|Doube|滑变结束频率 Hz||1600|预定义模型为雷达信号侦测器时的输入|
      |SlidePRF\_num|double|滑变频率个数||4|预定义模型为雷达信号侦测器时的输入|
      |SlidePRF\_RoundNumber|double|滑变组数||30|预定义模型为雷达信号侦测器时的输入|
      |JitterPFR\_PRF|double|抖动PRF Hz||1000|预定义模型为雷达信号侦测器时的输入|
      |JitterRange|double|抖动范围||0\.01|预定义模型为雷达信号侦测器时的输入|
      |JitterPFR\_PulseNum|double|抖动脉冲个数||100|预定义模型为雷达信号侦测器时的输入|
      |rf\_mode|Int|载频模式|<p>0:固定</p><p>1:捷变</p><p>2:跳变</p>|0|预定义模型为雷达信号侦测器时的输入|
      |pw\_mode|Int|脉宽模式|<p>0:固定</p><p>1:捷变</p>|0|预定义模型为雷达信号侦测器时的输入|
      |BeamMode\_in|Int|工作模式图|<p>0:跟踪</p><p>1:余弦扫描</p><p>2:辛克扫描</p><p>3:休眠</p>|0|预定义模型为雷达信号侦测器时的输入|
      |BeamRound|double|扫描模式下轮数||10|预定义模型为雷达信号侦测器时的输入|
      |FluMode\_in|Int|目标起伏特性|<p>0:无起伏</p><p>1:swerling1</p><p>2:swerling2</p><p>3:swerling3</p><p>4:swerling4</p>|0|预定义模型为雷达信号侦测器时的输入|
      |RCS\_avg|double|目标的平均RCS dB||5\.0|预定义模型为雷达信号侦测器时的输入|
      |Tx\_Longitude|double|雷达设备经度||1\.0|预定义模型为雷达信号侦测器时的输入|
      |Tx\_Latitude|double|雷达设备纬度||1\.0|预定义模型为雷达信号侦测器时的输入|
      |Tx\_Altitude|double|雷达设备高度||1\.0|预定义模型为雷达信号侦测器时的输入|
      |power|double|发射设备功率dbm||80\.0|预定义模型为雷达信号侦测器时的输入|
      |radertar\_freq|double|发射设备频率MHz||10\.0|预定义模型为雷达信号侦测器时的输入|
      |rx\_power|double|接收机功率dbm||20|预定义模型为雷达信号侦测器时的输入|
      |Tar\_Longitude|double|目标经度||10\.0|预定义模型为雷达信号侦测器时的输入|
      |Tar\_Latitude|double|目标纬度||10\.0|预定义模型为雷达信号侦测器时的输入|
      |Tar\_Altitude|double|目标高度||10\.0|预定义模型为雷达信号侦测器时的输入|
      |isjam|bool|是否添加干扰||true|预定义模型为雷达信号侦测器时的输入|
      |Longitude|double|干扰机经度||10\.0|预定义模型为雷达信号侦测器时的输入|
      |Latitude|double|干扰机纬度||10\.0|预定义模型为雷达信号侦测器时的输入|
      |Altitude|double|干扰机高度m||100|预定义模型为雷达信号侦测器时的输入|
      |jam\_direction|double|干扰机方向|-PI:PI|PI|预定义模型为雷达信号侦测器时的输入|
      |jam\_range|double|干扰范围m||100000000|预定义模型为雷达信号侦测器时的输入|
      |jam\_power|double|干扰功率dbm||100|预定义模型为雷达信号侦测器时的输入|
      |dTOA|double|脉冲到达时间||48\.0|预定义模型为雷达情报分析过程时的输入|
      |fDOA|double|脉冲到达角||30\.0|预定义模型为雷达情报分析过程时的输入|
      |fPA|double|脉冲幅度||102\.70685282735658|预定义模型为雷达情报分析过程时的输入|
      |fPW|double|脉冲宽度||11\.306666666666667|预定义模型为雷达情报分析过程时的输入|
      |fRF|double|脉冲频率||16\.255297979049605|预定义模型为雷达情报分析过程时的输入|

      表 21<a name="_toc19973"></a> 输出数据

      |字段|类型|说明|备注|
      | :-: | :-: | :-: | :-: |
      |nRFType|Int|1：固定，2：脉组捷变，3：脉间捷变，4：分集，其它：未知|预定义模型为雷达情报分析过程时的输出|
      |vRF|vector<float>|全部载频值|预定义模型为雷达情报分析过程时的输出|
      |vPW|vector<float>|脉宽值|预定义模型为雷达情报分析过程时的输出|
      |nPRIType|Int|1：固定，2：脉组参差，3：脉间参差，4：抖动，5：滑变，其它：未知|预定义模型为雷达情报分析过程时的输出|
      |vPRI|vector<float>|全部重频值|预定义模型为雷达情报分析过程时的输出|
      |fDOA|double|辐射源角度|预定义模型为雷达情报分析过程时的输出|
      |fPA|double|辐射源PA平均值|预定义模型为雷达情报分析过程时的输出|
      |SortTime|double|分选时间|预定义模型为雷达情报分析过程时的输出|
      |SortAccuracy|double|分选正确率|预定义模型为雷达情报分析过程时的输出|
      |Pd|double|截获概率|预定义模型为雷达信号侦测器时的输出|
      |CaptureTime|double|截获时间|预定义模型为雷达信号侦测器时的输出|
      |dTOA|double|脉冲到达时间|预定义模型为雷达信号侦测器时的输出|
      |fDOA|double|脉冲到达角|预定义模型为雷达信号侦测器时的输出|
      |fPA|double|脉冲幅度|预定义模型为雷达信号侦测器时的输出|
      |fPW|double|脉冲宽度|预定义模型为雷达信号侦测器时的输出|
      |fRF|double|脉冲频率|预定义模型为雷达信号侦测器时的输出|

   1  流程图

      算法流程如[图 17](#_ref23654)所示：

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.069.png)

      <a name="_ref23654"></a>图 18<a name="_toc683"></a> 雷达对抗侦察设备数据产生流程图

   1  步骤详细说明
1) 雷达信号侦测器：按TOA排序：将所有雷达目标的IQ信号按TOA从小到大排序。例如：

   目标A：TOA = [t₁, t₃, t₅, ...]

   目标B：TOA = [t₂, t₄, t₆, ...]

   交替排列：按TOA顺序交替排列信号，形成连续数据集：

   侦察信号 = [A(t₁), B(t₂), A(t₃), B(t₄), A(t₅), B(t₆), ...]

1) 参数测量：信道化接收机对输入信号进行多通道处理，首先对各信道数据进行相位差分，将相位差转换得到的局部频率与对应信道的中心频率相加，得到每个时间点的精确频率估计值。该过程在每个信道独立进行，最终通过时域选大处理获得每个时刻的最优频率(RF)估计；然后在时域上选择每个时刻幅度最大的信道数据，结合门限检测和形态学处理（去毛刺、填充空点）提取有效脉冲，最后通过脉冲标定测量每个脉冲的到达时间(TOA)、脉宽(PW)和幅度(PA)等参数，输出符合要求的脉冲描述字(PDW)。
1) 雷达情报分析过程：对参数测量的PDW作为输入，PW和DOA聚类、RF聚类用于判断是否捷变、CDIF、固定序列抽取、参差序列抽取、分类总结。
   1  算法详细介绍
      1  雷达信号侦测器参数测量

         详细参数测量如[图 18](#_ref2739)所示：

         ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.070.png)

         <a name="_ref24719"></a><a name="_ref201088327"></a><a name="_ref2739"></a>图 19<a name="_toc8014"></a> 侦察参数测量算法流程图

1) 由于线性调频信号为宽带信号，采用信道化接收相比于常规的下变频滤波可大幅度提升抽取比例，从而大大减少数据处理量，由于划分多组信道可以提升测频精度；同时采用多相结构的信道化技术将进一步减少滤波所带来的计算量。本模型信道化分如下[图 19](#_ref3050)所示：

![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.071.png)<a name="_ref3050"></a>图 20<a name="_toc12145"></a> 信道划分

![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.072.png)信道化多相结构算法结构如[图 20](#_ref3131)所示：

<a name="_ref3131"></a>图 21<a name="_toc12509"></a> 信道化多相结构

在脉冲检测模块中，基于设定的检测门限，首先对每个时间点的信号进行初步筛选：若某时间点对应的幅度值超过检测门限，则将其标记为潜在脉冲信号，反之标记为非脉冲信号，以此快速定位可能包含有效脉冲的时间区域。

针对实际信号中可能存在的短暂孤立噪声干扰（毛刺），尽管其幅度超过门限但并非真实脉冲，模块通过分析信号的时域连续性进行剔除——若某时间点幅度超门限，但其前后相邻时间点幅度均低于门限，则判定为毛刺并清除标记，确保脉冲信号需具备时域连续性以避免误判。

为解决信号中因噪声或干扰导致部分脉冲点幅度低于门限（空点）而打断脉冲连续性的问题，模块允许脉冲信号在局部范围内波动：若某时间点幅度虽低于门限，但其前后相邻时间点均为有效脉冲信号，则强制标记该点为有效，从而恢复完整脉冲结构。最后，对标记结果进行无效数据清理，将非脉冲时间点对应的幅度和频率置零，避免干扰后续参数测量。

整个模块通过“门限检测→毛刺剔除→空点填充→无效数据清理”的流程，在时域上对信号进行精细化处理，既排除了噪声干扰，又完整保留了真实脉冲的时域特征。

在PDW（脉冲描述字）测量模块中，首先通过信道选择与相位差分实现频率测量：对每个信道的时间序列进行相位解算，利用相邻时间点的相位差分（结合2π模糊度修正）计算瞬时频率，并结合信道中心频率标定绝对频率值；随后在时域上遍历所有信道，选取每个时间点上幅度最大的信道数据作为候选脉冲信号，通过动态门限检测（基于信号平均幅度设定）初步标记潜在脉冲，再采用“去毛刺”（孤立点过滤）和“空点填充”（连续性修复）规则优化标记结果，最终对标记为有效的脉冲区间进行参数聚合，计算脉冲到达时间（TOA）、脉宽（PW）、载频（RF）及功率（PA），其中频率通过瞬时频率的时域积分与信道频点校正获得，脉宽基于采样率换算为实际时间，功率采用幅度均值转换，最终生成标准化的脉冲描述字（PDW）并过滤短脉宽噪声，输出包含到达时间、载频、脉宽及功率等参数的特征向量。

PDW 输出模块则是对测量得到的 PDW 进行筛选，将符合脉宽要求的脉冲描述字存储到待雷达情报分析PDW 向量中，完成最终的输出。

1  雷达情报分析（分选）过程

   详细雷达分选算法流程图如[图 21](#_ref3935)所示：

![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.073.png)

<a name="_ref3935"></a>图 22<a name="_toc4584"></a> 雷达分选算法流程图

1) 本模块聚类采用K-Means算法，通过最小化样本到所属簇中心的平方误差（SSE），将数据划分为 K 个簇，适用于数值型数据的快速聚类。其中最小化样本到所属簇中心的平方误差的目标函数如下式：

   J=i=1Kx∈Cix−μi2

- 式中Ci表示第* i个簇，μi​ 是簇中心（均值向量），x 是样本点。
- 算法通过迭代优化 J来找到最优的簇划分。

  簇中心更新公式如下式：

  μi=1Cix∈Cix

- 每次迭代后，簇中心更新为簇内样本的均值。

  样本分配规则如下式：

  c(t)=arg min1<i<Kx(t)−μi(t−1)2

- 每个样本被分配到距离最近的簇中心（欧氏距离）。
1) CDIF基于脉冲重复间隔（PRI）的时域分选，通过构建累积差值直方（CDH）统计脉冲到达时间（TOA）的差值分布。 

   脉冲到达时间差值计算：

   对脉冲序列 {t1​,t2​,...,tN​}，计算所有相邻脉冲的差值（一阶差分）及高阶差值：

   ∆ti,j=tj−ti  (1<i<j<N)

   累积差值直方图（CDH）统计：

   统计所有差值 ∆t 的出现频次 H(∆t)，并累积到直方图中：

   H∆t=i,jδ(∆t−(tj−ti))

- δ(∙)为克罗内克函数（离散情况）或狄拉克函数（连续情况）。

  门限检测与PRI提取：

  通过自适应门限（如基于直方图均值或噪声基底）筛选显著峰值，对应真实PRI：

  PRIk=argmax∆t[H∆t>θ∙mean(H)]

- θ为门限系数，mean(∙)代表取均值
1) vConstPRI表示通过固定序列抽取的PRI个数，以区分出固定重频，抖动，以及其它PRF模式。

   vStaggerPRI表示参差序列抽取的PRI的种类个数，区分出抖动以及其它PRF模式。

   avgSum表示二阶差分序列的绝对值均值，用于区分出参差和滑变。

   maxDiffRatio表示显著频率=跳变脉冲/脉冲总数，用于判断脉间/组捷变。

   1  <a name="_toc2228"></a>光电对抗侦察设备数据产生算法模型
      1  应用场景

         在军事领域，光电对抗侦察设备数据产生算法模型可用于侦察敌方军事目标，如喷气式飞机、军舰等，通过多波段辐射采集和大气传输衰减建模，生成高精度的侦察数据，用于目标识别、威胁评估和作战规划。在边境监控中，该模型能够实时检测非法入侵行为，适应复杂气象条件，为边境安全提供预警。在民用安防领域，算法模型可用于监控城市关键区域，识别异常行为，提升安防系统智能化水平。

      1  总体思路

         通过多波段辐射采集（如中波3-5μm、长波8-14μm红外辐射），结合距离相关的大气传输衰减建模，计算目标在焦平面上的像元占用数，构建发动机与蒙皮辐射的联合信噪比模型，并基于Weibull模型将信噪比转换为侦察数据生成概率，从而完成从目标辐射信号到侦察数据的转换。

      1  输入输出数据

         表 22<a name="_toc1712"></a> 输入数据

         |字段|类型|说明|可选值|参考值|备注|
         | :-: | :-: | :-: | :-: | :-: | :-: |
         |D|浮点数|光学孔径|-|0\.15 m|引用文献[1]|
         |f|浮点数|焦距|-|0\.6 m|-|
         |NETD|浮点数|噪声等效温差|-|20 mK|制冷型探测器|
         |pixelSize|浮点数|像元尺寸|-|15 μm|-|
         |FOV|浮点数|视场角|-|2\.5°|-|
         |T\_engine|浮点数|发动机尾喷口温度|-|650 K|F-35特性|
         |T\_skin|浮点数|蒙皮摩擦温度|-|320 K||
         |A\_engine|浮点数|发动机有效辐射面积|-|0\.8 m²|-|
         |A\_skin|浮点数|蒙皮辐射面积|-|50 m²|-|
         |atmTransMWIR|函数句柄|中波红外大气衰减函数|-|指数衰减||
         |atmTransLWIR|函数句柄|长波红外大气衰减函数|-|指数衰减||
         |R|向量|探测距离|-|10-300 km|步长1 km|

         表 23<a name="_toc10106"></a> 输出数据

         |字段|类型|说明|
         | :-: | :-: | :-: |
         |Pd|向量|探测概率随距离变化的向量（与R对应）|
         |figure|图形|探测概率曲线图|

      1  流程图

         算法流程如[图 22](#_ref29982)所示：

         ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.074.png)

         <a name="_ref29982"></a>图 23<a name="_toc26613"></a> 光电对抗侦察设备数据产生算法模型流程图

      1  步骤详细说明
1) 系统参数设定：
- 光学系统参数：孔径D、焦距f、视场角FOV
- 探测器参数：NETD、像元尺寸
- 环境模型：大气衰减函数
1) 飞机参数设定：
- 热源特性：发动机温度T\_engine，蒙皮温度T\_skin
- 辐射面积：发动机有效面积A\_engine，蒙皮面积A\_skin
1) 多波段辐射积分：
- 中波红外波段(3-5μm)计算发动机辐射
- 长波红外波段(8-14μm)计算蒙皮辐射
- 使用梯形法数值积分trapz
1) 探测距离分析：
- 距离范围：10km至300km，步长1km
- 对每个距离点进行计算
1) 大气传输衰减：
- 中波波段：τMWIR=exp(−0.12R/1000)
- 长波波段：τLWIR=exp(−0.07R/1000)
1) 目标投影计算：
- 瞬时视场角：θifov=fpsize
- 像元占用数：npix=(R⋅θifov)2Atarget
1) 复合信噪比模型：
- 信噪比计算公式：SNR=NETDLsource⋅τatm⋅npix​
- 总信噪比合成：SNRtotal=SNRengine2+SNRskin2
1) 探测概率转换：
- 改进Weibull模型：Pd=1−exp[−(1.8SNRtotal)4.2]
1) 结果可视化：
- 半对数坐标绘制探测概率曲线
- 标注50%探测概率对应距离
  1  算法详细介绍

     核心物理模型：

1) 波段积分辐射模型：

Lband=λ1λ2λ5expλTc2−1c1 dλ

1) 其中：
   1. c1=3.7418×10−16 W\cdotpm−2
   1. c2=1.4388×10−2\mboxmdotpK
   1. *T*：绝对温度(K)
1) 目标投影模型：

​\
npix=R2⋅psize2Atarget⋅f2

1) 其中：
   1. *R*：目标距离(m)
   1. *f*：系统焦距(m)
   1. *p*size​：像元尺寸(m)
1) 探测概率模型：

Pd(SNR)=1−exp[−kSNRm]

1) 其中：
   1. k=1.8, m=4.2 为实验拟合参数
      1  <a name="_toc25721"></a>雷达探测模型数据产生模型
         1  <a name="_toc8472"></a>模型描述

            雷达探测模型数据产生模块详细说明了雷达探测模型数据产生算法模型，通过该模型可实现雷达探测模型数据产生功能。

         1  <a name="_toc14501"></a>实现设计
            1  <a name="_toc4663"></a>应用场景

               本模块应用场景广泛分布于军事防御、民用航空、安全保障等多个领域。在军事方面，雷达是空中预警与防空反导系统的核心装备，能在远距离精确探测战机、导弹等高速飞行目标，为作战指挥提供实时态势感知和目标引导信息；在民用领域，雷达广泛应用于空中交通管制、机场空域监视与航班运行调度，保障飞行安全；在公共安全和应急管理方面，雷达被用于大型活动的低空安防、边境巡防以及要地空域管控，有效提升城市与关键基础设施的空中安全防护能力。

               主要应用方向集中于对不同空中目标的探测、识别与连续跟踪，包括高空高速目标（如军用飞机、弹道导弹）、中低空目标（如直升机、巡航导弹）以及低空慢速小目标（如无人机、气球、滑翔伞等）。

            1  <a name="_toc19329"></a>总体思路

               首先通过回波仿真生成雷达信号，结合目标参数和起伏模型模拟真实目标特性，并添加环境噪声以增强仿真真实性。随后对回波数据进行预处理，通过脉冲压缩提高距离分辨率，利用MTI滤除杂波，再经MTD进行多普勒分析以提取运动目标速度信息。接着通过检测聚类算法将离散点迹合并为潜在目标，在跟踪模块对目标进行跟踪，得到目标的航迹信息。跟踪模块介于雷达检测结果和后端应用之间，跟踪模块所做的事情主要就是优化检测结果，管理检测结果以使检测结果更好服务于应用。

            1  <a name="_toc6541"></a>输入输出数据

               表 24<a name="_toc19552"></a> 输入数据

               |字段|类型|说明|可选值|参考值|说明|
               | :-: | :-: | :-: | :-: | :-: | :-: |
               |radar\_mode|str|雷达工作模式|TAS/TWS|TAS||
               |targets|dict|目标的位置、速度等信息||<p>tarPos=[[10000,0,300][20000, 10000, 5000] [30000,-20000,1000]]</p><p>travel=[[150, 10, 0], [90, 20, 0], [-120, 20, 0]]</p><p>Angle = [10, 20, 30]</p><p>type = [1, 1, 1]</p><p>RCS = [1, 1.5, 1]</p>|动态目标数据|
               |radar\_pos|np.ndarray|雷达位置信息||[0, 0, 0]|笛卡尔坐标系|
               |BandWidth|float|信号带宽||1e6|单位为Hz|
               |RF|float|雷达射频||3e9|单位为Hz|
               |Fs|float|采样频率||2e6|单位为Hz|
               |Lambda|float|雷达工作波长||C/RF|单位为米|
               |PRF|float|脉冲重复频率||2000|单位为Hz|
               |TimeWidth|float|脉冲宽度||0\.1\*PRT|单位为秒|
               |Pt|float|雷达发射功率||1000|单位为W|
               |F|float|噪声系数||3|无|
               |TG\_max|float|发射增益||25|单位为dB|
               |RG\_max|float|接收增益||25|单位为dB|
               |scan\_angle|float|雷达扫描角度范围||120|单位为度|
               |sim\_time|float|雷达仿真总时长||50|单位为秒|
               |Nx|int|x方向阵元数||16|整数|
               |Ny|int|y方向阵元数||8|整数|
               |elev\_min|float|最小俯仰角||15|单位为度|
               |elev\_max|float|最大俯仰角||75|单位为度|
               |overlap\_ratio|float|波束重叠比例||0\.3||
               |beam\_spacing|float|波束间隔||8\.925||
               |elev\_centers|np.ndarray|波束中心角度||生成数组|单位为度|
               |scan\_time|float|扫描模式持续时间||2|单位为秒|
               |track\_time|float|跟踪模式持续时间||1|单位为秒|
               |flag\_mti|int|MTI处理标志|0/1|1||
               |cfar\_window|dict|CFAR检测窗口||{'mrange\_size': 16, 'prange\_size': 2, 'mazi\_size': 8, 'pazi\_size': 2}|单位为个|
               |threshold|float|CFAR检测阈值||15|单位为DB|
               |rotation\_speed|float|雷达扫描速度||60||
               表 25<a name="_toc27875"></a> 输出数据

|字段|类型|说明|
| :-: | :-: | :-: |
|detect\_cart0|np.ndarray|目标三坐标检测结果|
|outputs|list|目标当前帧跟踪三坐标位置与标号|
|vels|np.ndarray|目标当前帧跟踪目标速度|
|track\_lines|list|目标跟踪轨迹|

1  <a name="_toc7647"></a>流程图

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.075.png)

   图 24<a name="_toc27408"></a> 雷达探测模型数据产生模型流程图

1  <a name="_toc28925"></a>步骤详细说明
1) 雷达回波仿真：初始化雷达系统参数(频率、PRF、波束宽度等)。设置仿真环境参数(仿真时间、扫描模式等)。
1) 目标参数设置：定义目标初始位置、速度和RCS(雷达截面积)。选择目标起伏模型(Swerling模型等)。
1) 雷达回波生成：生成线性调频信号，接收时采用匹配滤波器压缩脉冲。
1) 脉冲压缩：使用匹配滤波处理回波信号，提高距离分辨率。
1) MTI处理(动目标显示)：使用对消技术抑制静止杂波，保留运动目标的回波信号。
1) f.MTD处理(动目标检测): 对多脉冲回波进行FFT处理，提取目标多普勒频率计算速度。
1) 目标检测: 使用CFAR(恒虚警率)检测算法，应用DBSCAN聚类算法去除虚警和杂波。
1) 角度估计：使用MUSIC超分辨算法，估计目标的方位角和俯仰角；
1) 参数预设：设置如目标运动模型、航机管理的各类条件参数；
1) 航机结构体初始化：预设每条航迹应该包含的参数，如滤波过程的各类矩阵、航迹管理中的各种标识变量等。
1) 各类航迹目标状态预测：更新现有航迹在本帧下的目标位置预测值（实际工作时，帧与帧之间的循环从这里开始）
1) 数据关联：雷达本帧的检测结果与上一帧所有航迹的预测结果之间的关联。
1) 航迹更新模块：对于关联上的航迹，更新该条航迹结构体中个参数；对没有关联上的航迹，更新该条航迹结构体中各参数。对于已经确认丢失的航迹，该航迹将及时被删除；对与没有关联上的目标，基于是够满足预设条件，为其新建航迹。
1) 跟踪结果输出：用于后续ADAS功能、可视化。
   1  <a name="_toc5398"></a>算法详细介绍
      1  <a name="_toc3939"></a>雷达探测模型数据产生
1) 目标模拟

   目标模拟根据由中频处理单元对信号实现目标特征模拟调制。接收信号在中频处理单元中，进行AD采样和正交变换后，采用DRFM储频进行长延时存储转发，多路相对独立的目标距离模拟，每一路目标使用数字DDS产生多普勒频移，可由不同特性的目标模型调制模拟目标RCS散射点相对起伏；经过独立调制后的多个目标合成后经送DA输出。 

   雷达目标调制模拟进行目标距离、速度以及RCS起伏。本系统中的模拟假目标的测量空间为时变的距离、速度两维空间，即任一时刻的假目标观测值为一个联合观测值：![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.076.png)

   其中距离参数![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.077.png)由时延参数![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.078.png)体现，![ref8]由多普勒调制参数![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.080.png)体现，假目标的加速度信息，将转换为![ref8]进行表现。

   多普勒模拟本质就是对速度及加速度模拟，多普勒![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.081.png)是目标的速度和加速度的直接反应，如果目标为匀速，则![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.081.png)为常数，如果目标有匀加速度，则![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.081.png)是随时间线性变化的。

   对信号进行幅度调制，由雷达方程可知，反射式雷达距离方程的飞行目标信号强度表达式为：

   pr=PtGtGtλ2σ4π2R4\
               

   式中：pr：单个脉冲回波功率；Pt：发射机峰值功率；Gt：雷达天线发射增益；Gr：雷达天线接收增益；λ：载波波长；σ：目标有效反射截面积；R：目标距离。

   根据目标航迹信息计算出目标距离R，便可计算出目标的信号强度。

   在雷达目标模拟系统数字仿真中，可以采用多种类型的目标模型。为了仿真目标幅度起伏以及角度闪烁效应，可以采用统计分析的方法建立目标的统计模型（如SwerlingI型、II型模型等），其统计参数（均值、方差、带宽）对目标视角变化。对于高分辨率雷达，仿真系统需要采用多散射中心目标模型。基于对散射点散射系数分布函数的不同假设，其中Swerling模型是一种典型的RCS的非参数模型。在目标运动过程中，其位置和自身姿态相对于雷达来说都在时刻变化，因此其瞬时RCS也是不断变化的。

   1. Swerling 0型

此类型目标的雷达截面积（RCS）是恒定的，不随时间变化，适用于反射截面积恒定的目标，例如金属圆球。

1. Swerling I目标模型

该模型目标幅度在每一次扫描内是恒定的，在扫描与扫描之间是变化的且其变化服从一定的概率分布。由于扫描与扫描之间时间相对较长，该起伏也被称之为慢起伏。雷达散射截面积（RCS）可以表示为

f(σ)=1σ0exp−σσ0,σ≥0*\


式中![ref6]为目标RCS的平均值。由于目标RCS的值s正比于回波的复电压模值的平方，定义随机变量A描述信号幅值，则目标幅值的概率密度函数可由目标RCS的表达式得到，结果如下。

f(A)=AA0exp−A22A02,A≥0,2A02=σ0\


此模型主要适用于大量独立散射体组成的目标，每个散射体都有独立的RCS且每个散射体都不占主导地位。该目标模型主要适用于表面较大的飞机、气象杂波以及地杂波。

1. Swerling II目标模型

此模型与Swerling I目标模型一样具有相同的概率密度函数，只不过相对于Swerling I目标模型在一次扫描内幅度不变的情况不同，其幅度在每个脉冲之间都是变化的且变化服从瑞利分布。由于此起伏相对于扫描到扫描之间的起伏更快，故称之为快起伏。此目标模型主要适用于直升飞机、雨杂波、地杂波等。

1. Swerling III型目标起伏模型

如同Swerling I型目标起伏一样，在一次扫描期间内目标幅度是不变的，在扫描到扫描间目标幅度是变化的。与Swerling I型目标不同点在于其概率密度函数所服从的分布不同，Swerling III型目标RCS概率密度函数服从如下分布：     \
f(σ)=4σσ02exp−2σσ0,σ≥0

由于目标RCS的值![ref7]正比于回波的复电压模值的平方，定义随机变量A描述信号幅值，则目标幅值的概率密度函数可由目标RCS的表达式得到，结果如下。       

f(A)=9A32A04exp−3A22A02,A≥0,4A02/3=σ0\


此模型主要适用于描述一个大散射体和许多独立的小散射体组成的目标或只有一个大散射单独组成的目标。此目标模型主要适用于包括火箭、长且窄的飞机、导弹等具有细而长的表面的物体。

1. Swerling IV型目标

其起伏属于快起伏，即在脉冲与脉冲之间幅度是变化的且其幅度变化服从的概率密度函数与Swerling III模型目标幅度起伏服从的概率密度函数一致。此模型同Swerling III模型一样，适用于描述一个大散射体和许多独立的小散射体组成的目标或只有一个大散射单独组成的目标。

1) 回波模拟

   线性调频（Linear Frequency Modulation）信号,接收时采用匹配滤波器（Matched Filter）压缩脉冲。脉冲压缩雷达能同时提高雷达的作用距离和距离分辨率。这种体制采用宽脉冲发射以提高发射的平均功率，保证足够大的作用距离；而接受时采用相应的脉冲压缩算法获得窄脉冲，以提高距离分辨率，较好的解决雷达作用距离与距离分辨率之间的矛盾。

   LFM信号的数学表达式为：

   s(t)=rect(tT)ej2π(fct+k2t2)

   式中fc为载波频率，rect(tT)为矩形信号，

   rect(tT)=1  ,      |tT|≤10   ,     elsewise

   K=BT，是调频斜率，于是，信号的瞬时频率为fc+Kt (−T/2≤t≤T/2)，如下图所示： 

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.082.jpeg)

   `    `up-chirp (K>0)                   down-chirp (K<0)

   图 25<a name="_toc26431"></a> 典型的chirp信号

   将其中的up-chirp信号重写为：	     \
   s(t)=S(t)ej2πfct

   式中，

   S(t)=rect(tT)ejπKt2

   是信号s(t)的复包络。由傅立叶变换性质，S(t)与s(t)具有相同的幅频特性，只是中心频率不同而以，因此，MATLAB仿真时，只需考虑S(t)。以下MATLAB程序产生2.9式的chirp信号，并作出其时域波形和幅频特性，如下图所示。

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.083.png)

   图 26<a name="_toc6524"></a> LFM信号的时域波形和幅频特性

   1  雷达信号处理
1) 脉冲压缩

   脉冲压缩指雷达在发射时采用宽脉冲信号，接收和处理回波后输出窄脉冲。脉冲压缩技术是匹配[滤波](https://baike.baidu.com/item/%E6%BB%A4%E6%B3%A2)理论和相关接收理论的一个很好的实际应用。很好地解决了这样的一个问题：在发射端发射大时宽、带宽信号，以提高信号的发射能量，而在接收端，将宽脉冲信号压缩为窄脉冲，以提高雷达对目标的距离分辨精度和距离分辨力。该技术解决了雷达远距离探测与高精度测距性能不可兼顾的问题，是现代雷达中不可缺少的关键技术。

   脉冲压缩的DSP处理方法有时域相关或频域相乘。对于点数较多的回波信号，采用频域相乘方法可以获得较快的运算速度。频域脉冲压缩的原图如下图所示。

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.084.png)

   图 27<a name="_toc23935"></a> 脉冲压缩处理流程图

   DSP对采样后的数据进行FFT变换，变换至频域后，与其匹配滤波器频率数据进行复数相乘，相乘后，再与复数补偿因子进行相乘解决脉冲间距离走动问题，最后将结果做IFFT，重新变换回时域。其中，FFT点数、复数相乘点数、IFFT点数均为1024点。

   信号![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.085.png)的匹配滤波器的时域脉冲响应为：

   `	`ℎ(t)=s∗(t0−t)	

   ![ref9]是使滤波器物理可实现所附加的时延。理论分析时，可令![ref9]＝0，公式，

   `	`ℎ(t)=s∗(−t)	 

   将上述公式合并得:

   `	`ℎ(t)=rect(tT)e−jπKt2×ej2πfct

   `                 `![LFM信号的匹配滤波1](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.087.jpeg)

   图 28<a name="_toc31412"></a> LFM信号的匹配滤波

   如上图,![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.085.png)经过系统![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.088.png)得输出信号![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.089.png)，当![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.090.png)时,

   s0(t)=t−T/2T/2ejπKt2e−j2πKtudu=ejπKt2e−j2πKtu−j2πKtT/2t−T/2×ej2πftt=sinπK(T−t)tπKtej2πfct

	

   当![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.091.png)时,	

   s0(t)=−T2t+T2ejπKt2e−j2πKtudu=ejπKt2e−j2πKtu−j2πKtt+T2−T2×ej2πfct=sinπK(T+t)tπKtej2πftt

   合并上述两式：

   s0(t)=TsinπKT(1−|t|T)tπKTtrect(t2T)ej2πfct\
	

   即为LFM脉冲信号经匹配滤波器得输出,它是一固定载频![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.092.png)的信号。当![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.093.png)时，包络近似为辛克（sinc）函数。

   S0(t)=TSa(πKTt)rect(t2T)=TSa(πBt)rect(t2T)

   ![匹配滤波的输出信号](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.094.jpeg)

   图 29<a name="_toc4633"></a> 匹配滤波的输出信号

   如上图，当![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.095.png)时，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.096.png)为其第一零点坐标；当![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.097.png)时，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.098.png)，习惯上，将此时的脉冲宽度定义为压缩脉冲宽度。

   τ=12B×2=1B

   LFM信号的压缩前脉冲宽度T和压缩后的脉冲宽度![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.099.png)之比通常称为压缩比D，\
   D=Tτ=TB

   上述公式表明，压缩比也就是LFM信号的时宽频宽积。

   由上述公式，s(t),h(t),so(t)均为复信号形式，MATLAB仿真时，只需考虑它们的复包络S(t),H(t),So(t)。以下MATLAB程序段仿真了[图](#_ref505548317)所示的过程，并将仿真结果和理论进行对照。仿真结果如下图：

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.100.png)

   图 30<a name="_toc31705"></a>  LFM信号的匹配滤波

   图中，时间轴进行了归一化。图中反映出理论与仿真结果吻合良好。第一零点出现在![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.101.png)（即![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.102.png)）处，此时相对幅度-13.4dB。压缩后的脉冲宽度近似为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.103.png)（![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.104.png)），此时相对幅度-4dB,这理论分析一致。

   对于点数较多的回波信号，采用频域相乘方法可以获得较快的运算速度对采样后的数据进行FFT变换，变换至频域后，与其匹配滤波器频谱数据进行复数相乘，最后将结果做IFFT，重新变换回时域。以下MATLAB代码仿真了上述过程，并将输入信号以及脉压后的输出信号进行了比较。

1) MTI

   动目标显示技术是基于回波多普勒信息的提取而区分运动目标和固定目标 包括低速运动的杂波等。它的主要任务是根据运动目标回波和杂波在频谱结构上差别从频率上将它们分开以达到抑制固定杂波而显示运动目标的目的。 

   杂波功率谱可以分为两部分：

   C(f)=C1(f)+N0

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.105.png)

   图 31<a name="_toc6869"></a> 地杂波的功率谱

   其中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.106.png)为均匀分量的功率谱密度它取决于系统的稳定性。稳定性越高从值越小。![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.107.png)是由杂波特性包括天线扫描和杂波内部起伏决定的梳状分量天线扫过目标时收到的回波脉冲数越少杂波内部起伏越大则梳状谱就越宽。根据最佳滤波理论，当杂波功率谱![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.108.png)和信号频谱![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.109.png)已知时，最佳滤波器的频率响应是：

   H(f)=S∗(f)⋅e−j2πftsC(f)

   式中，\*表示共轭，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.110.png)是使滤波器能够实现而附加的延迟时间。式（2.20）的滤波器可以分成两个级联的滤波器![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.111.png)和![ref10]，其传递函数形式为：

   H1(f)=1C(f)

   H2(f)=S∗(f)⋅e−j2πfts

   其中,![ref11]是用来实现抑制杂波的，![ref10]是用来实现脉冲信号匹配的。对于动目标显示雷达，它主要是抑制杂波而使各种不同速度的目标回波能够通过。因此，它所用的MTI滤波器相当于这里的![ref11]。实际上对于杂波抑制滤波器 只要能使滤波特性的凹口位置和宽度与杂波谱一致,就能够达到杂波抑制的效果。杂波抑制滤波器又称数字对消器.下面简单介绍一次相消器和二次相消器。一次对消滤波器的结构如下图所示。对消公式及其Z变换为:

   y(n)=x(n)−x(n−1)

   Y(z)=X(z)−z−1X(z)

   传递函数为：

   H(z)=1−z−1=z−1z

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.114.png)

   图 32<a name="_toc7559"></a> 一次对消滤波器结构

1) 动目标处理

   MTD是基于窄带多谱勒滤波器组器处理，能够检测强地物杂波中的低速目标和切向向飞行的大目标；不仅能抑制平均多谱勒为零的固定杂波，而且还能抑制如气象、鸟群等引起的慢动杂波。

   杂波包括地物、海浪、云雨及敌人施放的金属箔条等。区分目标与杂波的基础是它们在速度上的差别。

1) 地物杂波位于零频附近，滤波器由于在零频附近有比较深的凹口而对零频附近的地物杂波有比较好的抑制，零频以外的信号为动杂波或者目标。
1) 动杂波和目标在运动速度上一般相差很大，如果采用MTD（窄带滤波器组）处理，则动杂波和目标会从不同的滤波器（即不同的频道）输出。动杂波在距离上具有比较强的相关性，目标信号一般仅会出现在一个距离单元上，根据这个相关性，在距离上通过CFAR方式来对动杂波加以抑制，采用了分频道恒虚警处理，可大大提高信杂比。

   设计16点的MTD滤波器，切比雪夫窗55db抑制，如下图所示。号处理软件重构设计中，根据脉组、脉冲数不同，MTD阶数可定义。

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.115.png)

   图 33<a name="_toc7903"></a>  MTI滤波器

   MTD滤波器需要对慢节拍中采样点，逐快节拍单元进行FFT处理。

1) 目标检测

   恒虚警率CFAR是ConstantFalse-AlarmRate的缩写。在雷达信号检测中，当外界干扰强度变化时，雷达能自动调整其灵敏度，使雷达的虚警概率保持不变，这种特性称为恒虚警率特性。恒虚警率检测是雷达目标自动检测的一个重要组成部分，作为从雷达中提取目标的第一步，是进一步识别目标的基础。虚警率是指侦察设备在单位时间内将噪声或其他干扰信号误判为威胁辐射源信号的概率。而恒虚警率检测则证明了检测算法的稳定性和可靠性。

   雷达信号恒虚警率检测就是要求虚警概率保持恒定，这主要是因为在雷达信号检测中，信号的最佳检测通常采用奈曼-皮尔逊准则，即在保持恒定的虚警概率的条件下，使正确检测的概率达到最大值。

   完成相参积累后，形成“距离-多普勒”二维分布图。对该二维分布图取模值处理后，形成CFAR(恒虚警)平面，若回波中存在目标信号，则二维分布图上会出现目标尖峰，如下图所示。

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.116.png)

   图 34<a name="_toc28415"></a> CFAR平面

   以CFAR 平面为基础，采用两维CFAR 检测方法，完成目标检测。CFAR处理原理如上图所示。在检测单元的两侧各留出一些保护单元，保护单元的总数略大于目标所占分辨单元数。同时，由于采用相参体制，可以联合利用距离维和速度维的一定数量的参考单元的平均值作为比较电平，在与检测单元进行比较，依据识别系数判断比较结果，从而判断目标的存在。

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.117.png)

   图 35<a name="_toc19218"></a> 二维CFAR原理图

   根据检测概率、虚警概率，可以设备二维CFAR中的单次检测信噪比为15dB，在此基础上采用二进制积累，采用3选2的准则，进行目标确认，以进一步减少噪声干扰的影响，进一步降低虚警概率。恒虚警检测按照检测的维数可以分为二维恒虚警以及一维恒虚警，先通过二维恒虚警进行初步的筛选，而后在二维恒虚警的结果上分别通过距离维和频率维完成更为精细的恒虚警检测。

1) 角度估计（两维music）

   MUSIC算法采用合理的信号和噪声模型，利用接收数据中信号子空间和噪声子空间的可分性（注：信号的协方差矩阵不是满秩的，噪声的协方差矩阵是含有相同元素的对角矩阵。此时，纯噪声的特征值与噪声加信号的特征值具有明显的可鉴别性。即纯噪声的特征值偏小，噪声加信号的特征值偏大，它们之间常常存在可区分的拐点。由此可以将整个空间划分为噪声子空间和信号子空间），估计噪声（或信号）子空间；然后利用信号子空间和噪声子空间之间的正交性构建空间谱，对信号源进行DOA进行估计。

   <a name="_toc3275"></a>假设从远场有K个互不相关的窄带信号，入射到一个阵元个数为M的平面阵列上。记第i个入射信号的方位角和俯仰角分别为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.118.png)和![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.119.png)。天线阵列的导向矢量为: 

   a(θi,φi)=[a1(θi,φi),a2(θi,φi),⋯,aM(θi,φi)]T=e−j2πλτ1,e−j2πλτ2,⋯,e−j2πλτMT

   其中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.120.png)表示第k个阵元所对应的阵列流形，λ表示入射信号的波长，τk=xkcosθicosφi+yksinθicosφi(k=1,2,⋯,M)，(xk,yk)为第*k*个阵元在空间中的坐标。

   接收信号的天线阵列中的第k个阵元在第n个采样时钟接受到的信号矩阵形式表示为：

   X(n)=A(θ,φ)S(n)+N(n)

   其中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.121.png)为天线阵列的阵列流形矢量，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.122.png)为入射信号矢量，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.123.png)为阵列收到的噪声矢量，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.124.png)为阵列流形接收到的信号矢量。

   阵列接收信号的协方差矩阵可以表示为： 

   R=E[XXH]=AE[SSH]AH+σ2I=ARSAH+σ2I*\
   `	`其中![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.125.png)表示入射信号的协方差矩阵，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.126.png)表示功率为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.127.png)的高斯白噪声。

   实际应用中天线阵列获取的信息是有限次的快拍，因此只能得到协方差矩阵的估计值![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.128.png)，其计算公式如下：

   R=1Ni=1NXXH

   由于接收信号的协方差矩阵![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.129.png)是对称矩阵，因此可以对其进行特征值分解，可以得到：

   R=UΛUT

   其中![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.130.png)为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.129.png)的特征向量构成的矩阵，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.131.png)是一个由特征值构成的对角矩阵。

   Λ=diagλ1,λ2,…,λM

   假设对角矩阵中的特征值降序排列，满足如下关系：

   λ1≥λ2≥⋯≥λK>λK+1=⋯=λM=σ2

   由前K个较大的特值构成的对角矩阵![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.132.png)，其对应的特征向量构成的矩阵![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.133.png)为信号子空间。由后![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.134.png)个较小的特征值构成的对角矩阵![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.135.png)，其对应的特征向量构成的矩阵![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.136.png)为噪声子空间。

   假设信号与噪声相互独立，因此信号子空间与噪声子空间是相互正交的，故信号阵列流行矢量与噪声子空间也具有正交性。

   aH(θi,φi)UN=0

   由于现实环境中有大量干扰因素存在，故上式不完全成立，即信号阵列流形与噪 声子空间并不完全具有正交关系。因此，需要构造二维谱函数:

   P2D−MUSIC(θ,φ)=1aH(θi,φi)UNUNHa(θi,φi)

   类比对于时域信号进行频谱分析，对于空间谱分析的目的在于得到信号在空间中的能量分布情况。当天线阵列的方向矢量与噪声子空间近似正交时，上式分母部分取极小值，空间谱函数在此时取得极大值，得到空间谱的谱峰。对空间谱进行谱峰搜索，就能够得到对应的方位角与俯仰角的角度，即为入射信号的方位角与俯仰角，至此便完成了对于信源的二维DOA估计。

   算法步骤：

1. 根据天线阵列接收到的信号得到其协方差矩阵的估计值![ref12];
1. 对![ref13]进行特征值分解，进而获得信号子空间![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.138.png)；和噪声子空间![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.136.png)；
1. 使得![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.139.png)不断变化，利用MUSIC谱函数计算峰值，即为波达方向的估计值。
   1  数据处理

      当雷达接收到回波数据后，首先利用一系列的信号处理技术对其进行处理，信号处理的结果作为目标跟踪的输入，最后输出目标航迹。目标跟踪的主要任务是通过目标当前时刻的位置预测目标下一时刻的位置，在获得下一时刻的量测值后，利用量测值与目标状态的预测值得到目标状态的最优估计值，从而实现对目标的稳定跟踪。雷达目标跟踪处理的基本流程如[图 35](#_ref27273)所示。

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.140.png)

      <a name="_ref27273"></a>图 36<a name="_toc17542"></a> 航迹跟踪

      卡尔曼滤波器是一种利用观测量对预测量进行加权修正，使得当前状态估计最优的线性无偏最小均方误差估计法，十分适用于线性时变系统非平稳过程。对于做匀速运动、匀加速运动、随机机动飞行或处于暂态过程的目标，卡尔曼滤波算法不仅能够实时处理，其适应能力也是最强的。因此，该算法广泛适用于通信、电力、计算机视觉和雷达信号处理等领域。

      滤波器模型：

      卡尔曼滤波器的算法框图下图所示，下面将对每一个滤波流程进行描述。

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.141.png)

      图 37<a name="_toc23413"></a> 卡尔曼滤波器的算法框图

1. 状态方程

   `	`X(k+1)=F(k)X(k)+V(k)	 

   式中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.142.png)第k时刻n×n阶状态转移矩阵，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.143.png)为第k时刻n×1阶状态向量，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.144.png)为第k时刻n×1阶零均值的高斯白过程噪声序列，协方差为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.145.png)。

1. 量测方程

   `	`Z(k+1)=H(k+1)X(k+1)+W(k+1)	 

   式中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.146.png)第k+1时刻m×1阶量测向量，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.147.png)为第k+1时刻m×n阶量测矩阵，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.148.png)为第k+1时刻m×1阶零均值高斯白量噪声序列，其协方差为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.149.png)。![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.144.png)与![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.148.png)不相关。

1. 状态的一步预测\
   X(k+1|k)=F(k)X(k|k)

   式中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.150.png)为第k及其之前时刻的量测值对![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.143.png)的最小均方估计。当过程本身和观测误差都服从高斯分布时，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.150.png)为最佳估计，即无偏估计。

1. 量测的预测	\
   Z(k+1|k)=H(k+1)X(k+1|k)

   式中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.151.png)为观测值集合![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.152.png)对观测值![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.146.png)的最小均方误差估计。

1. 新息（残差）

   v(k+1)=Z(k+1)−Z(k+1∣k)=H(k+1)X(k+1∣k)+W(k+1)

1. 一步预测的协方差

   利用预测误差X(k+1|k)=X(k+1)−X(k+1|k)，和已有观测值集合Zk，可求得第k+1时刻一步预测的协方差为

   P(k+1∣k)=E[X(k+1∣k)XT(k+1∣k)∣Zk]=F(k)P(k∣k)FT(k)+Q(k) 

   作为对称矩阵，P(k+1|k)可以对预测的不确定性进行衡量：该矩阵值越小，则表示卡尔曼预测的精度越高。

1. 新息协方差

   S(k+1)=E[v(k+1)vT(k+1)|Zk]=H(k+1)P(k+1|k)HT(k+1)+R(k+1)

   作为对称矩阵，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.153.png)可以衡量新息的不确定性：若该矩阵越小，则表示量测准确度越高。	 

1. 卡尔曼增益

   `	`K(k+1)=P(k+1∣k)HT(k+1)S−1(k+1)	 

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.154.png)相当于![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.155.png)和![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.153.png)的比值。若![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.156.png)较大，则表示![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.155.png)“大于”![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.153.png)，说明卡尔曼滤波器的预测误差偏大，不能过于依赖预测值![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.157.png)，应该以量测值![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.146.png)为主；当![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.156.png)较小，则表示![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.158.png)“小于”![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.153.png)，说明卡尔曼滤波器的观测误差偏大，不能过于依赖量测值![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.146.png)，应该以预测值![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.157.png)为主。

1. 状态更新	\
   X(k+1|k+1)=X(k+1|k)+K(k+1)v(k+1)

   滤波结果![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.159.png)需要在预测值![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.157.png)的基础上加入修正项，该修正项由卡尔曼增益![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.156.png)和新息![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.160.png)决定，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.156.png)起权重作用。

1. 协方差更新

   `	`P(k+1∣k+1)=P(k+1∣k)−K(k+1)S(k+1)KT(k+1)	 

   式中，状态估计协方差矩阵![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.161.png)对称且正定。

   状态初始化：

   状态初始化是目标跟踪的首要前提。卡尔曼滤波器适用于笛卡尔直角坐标系，对于做匀速直线运动的目标，只需对六维状态向量进行初始化，具体可表示为；

   `	`X(k)=x,x,y,y,z,zT	 

   式中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.162.png)、![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.163.png)和![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.164.png)依次表示目标在各轴的位置，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.165.png)、![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.166.png)和![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.167.png)依次表示目标在各轴的速度。

   初始状态估计为：	\
   X(2∣2)=Z1(2),Z1(2)−Z1(1)τ,Z2(2),Z2(2)−Z2(1)τ,Z3(2),Z3(2)−Z3(1)τT

   其中，Z(k)=Z1(k),Z2(k),Z3(k)T=x(k),y(k),z(k)T，x(k)=ρcosθcosε'，y(k)=ρsinθcosε'，z(k)=ρsinε'，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.168.png)、![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.169.png)和![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.170.png)分别为目标经点迹凝聚后的斜距、方位和俯仰，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.099.png)为雷达扫描间隔。

   量测噪声协方差可表示为：	\
   R(k)=r11r12r13r21r22r23r31r32r33=Aβρ2000βθ2000βε2AT

   其中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.171.png)、![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.172.png)和![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.173.png)依次表示为目标斜距、方位角和俯仰角测量误差的残差值，且

   A=cosθcosε'−ρsinθcosε'−ρcosθsinε'sinθcosε'ρcosθcosε'−ρsinθsinε'sinε'0ρcosε'\
	 

   初始协方差可表示为：

   P(2|2)=r11r11τr12r12τr13r13τr11τ2r11τ2r12τ2r12τ2r13τ2r13τ2r12r12τr22r22τr23r23τr12τ2r12τ2r22τ2r22τ2r23τ2r23τ2r13r13τr23r23τr33r33τr13τ2r13τ2r23τ2r23τ2r33τ2r33τ2\
	 

   滤波器从k=3时开始工作。

1) 三点航迹起始算法

   三点航迹起始算法主要利用三个连续时刻（![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.174.png)、![ref14]和![ref15]）的点迹信息进行航迹的起始：首先利用环形波门连接前两个时刻（![ref16]与![ref14]）的点迹，以形成暂时航迹；然后利用矩形波门将第三个时刻（![ref15]）的点迹与暂时航迹互联，形成确定航迹。这种算法得到的航迹质量较高，其具体步骤如下：

   步骤1:设雷达在第![ref16]时刻扫描到的点迹矩阵![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.178.png)中含有![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.179.png)个点迹。

   步骤2:设雷达在第![ref14]时刻扫描到的点迹矩阵![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.180.png)中含有![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.181.png)个点迹，其中![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.182.png)；以![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.178.png)中的第![ref17]个点迹![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.184.png)。为波门中心，形成环形波门![ref18]，记![ref19]中第![ref20]个点迹为![ref21]。

   步骤3：判断环形波门![ref18]范围内是否存在点迹![ref21]，即在设置目标的最小速度、最大速度和雷达采样间隔分别为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.189.png)、![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.190.png)和![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.191.png)的基础上，判断点迹![ref21]与波门中心![ref22]的欧式距离![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.193.png)是否满足下式：

   Vminτ≤Di≤Vmaxτ

   若是，则将点迹![ref22]与点迹![ref21]连在一起，形成1条暂时航迹，再遍历![ref17]与![ref20]，形成含有![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.194.png)条暂时航迹的暂时航迹矩阵![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.195.png)；之后执行步骤4；

   若不是，则把![ref14]赋值给![ref16]，返回步骤1。

   步骤4：设雷达在第![ref15]时刻扫描到的点迹矩阵![ref23]中含有![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.197.png)个点迹，其中w=e+1，根据第![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.198.png)条暂时航迹![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.199.png)的两个点迹![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.200.png)和![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.201.png)，得到第三点量测值的预测值![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.202.png)；以![ref24]为波门中心，形成矩形波门![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.204.png)；记![ref23]中离![ref24]距离最近的点迹为Zwz。

   步骤5：判断矩形波形门φ2范围内是否存在点迹Zwz，即判断在Nz维坐标系中，Zwz和Ze(w|w−1)中第![ref17]个分量Zwzi和Zei(w|w−1)是否均满足下式：

   Zwzi−Zei(w|w−1)≤KgS(w)ii	 i=1,2,…,Nz,Kg≥3.5

   若是，则将点迹Zwz与暂时航迹Us互联在一起，形成确定航迹![ref25]，假定![ref25]为真目标的航迹，记真目标的航迹标签![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.206.png)为![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.207.png)；若不是，则把![ref15]赋值给![ref16]，返回步骤1。

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.208.png)      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.209.png)

   (a) 扇形波门 					(b) 矩形波门（![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.210.png)）

   <a name="_ref14773"></a>图 38<a name="_toc11528"></a> 两种波门示意

   扇形波门如[图 37](#_ref14773)（a）所示，假定步骤2中![ref19]中含有5个点迹，若只有点迹![ref21]，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.211.png)落在扇形波门内，则共可建立3条暂时航迹。二维直角坐标系的矩形波门如[图 37](#_ref14773) (b)所示，此时维度![ref1]。假定步骤5中![ref26]落在以Ze(w|w−1)为波门中心的矩形波门内，则由点迹![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.200.png)和![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.201.png)形成的暂时航迹![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.199.png)将与点迹![ref26]互联，形成一条确定航迹。

1) 航迹互联算法

   基于贝叶斯理论，航迹互联算法共可分成两大类：一类是利用第k及其之前时刻的所有量测值进行航迹互联的最优贝叶斯算法，另一类是利用第k时刻的量测值进行航迹互联的次优贝叶斯算法。本章雷达跟踪系统采用的是第二类中的最近领域标准滤波器(Nearest Neighbor Standard Filter, NNSF)，其工作步骤为:

   步骤1：利用已有的确定航迹信息，得到第![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.194.png)时刻量测的预测值Z(k∣k−1)。

   步骤2：以Z(k∣k−1)为波门中心，生成一个椭球波门![ref27]，以限定与已有确定航迹互联的候选点迹范围。

   步骤3：判断椭球波门范围内存在的点迹数量是否等于0：若是，则表示波门内没有可以与确定航迹互联的点迹，需要采用后续介绍的记忆跟踪方法，将航迹预测点迹作为与航迹互联的点迹：若不是，则执行步骤4。

   步骤4：判断椭球波门![ref27]范围内存在的点迹数量是否等于1；若是，则将该点迹量测值作为与已有航迹互联的点迹；若不是，则需要在波门中选择与波门中心统计距离![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.214.png)最小的一个点迹作为与已有航迹互联的点迹，具体公式如下：

   d2Z=Zk−Zk∣k−1TS−1kZk−Zk∣k−1≤γ

   式中，参数![ref28]与椭球（圆）波门的大小有关，并影响着量测值![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.216.png)落入波门![ref27]内的概率。

   <a name="_ref27890"></a>表 26<a name="_toc13800"></a> ![ref2]维量测落入波门内的概率

   |![ref29]|1|4|9|16|
   | :-: | :-: | :-: | :-: | :-: |
   |![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.218.png)|0\.39|0\.87|0\.99|0\.9997|
   |![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.219.png)|0\.20|0\.74|0\.97|0\.9989|

   对于同一维度的坐标系，波门越大，候选点迹数量越多，概率就越高；对于同一大小的波门，坐标系维度越高，概率反而降低。通常情况，采用![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.220.png)的小型波门；当新生成的确定航迹首次与更新点互联时，可采用![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.221.png)的中型波门；当目标丢失后再次捕获用于互联的点迹时，可采用![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.222.png)的大型波门，以提高航迹的质量。对于特殊情况，可根据[表 26](#_ref27890)适当调整参数![ref29]的大小。

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.223.png)

   <a name="_ref16033"></a>图 39<a name="_toc24070"></a> 椭圆波门（![ref1]）

   [图 38](#_ref16033)给出了![ref1]时的椭圆波门，若第![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.194.png)时刻共有![ref30]和![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.225.png)两个量测点迹落在椭圆波门内，则选取离预测点迹Z(k∣k−1)最近的![ref30]与确定航迹互联在一起。

1) 航迹管理模块

   如前所述，航迹管理部分要完成目标航迹的建立、维护、删除等任务。但是不同于前述滤波和关联模块中有比较简洁和明确的处理流程，航迹管理部分面临的问题要更多，比如：1.满足何种条件时才可以为新目标新建一条航迹？2.满足何种条件时才可以判断目标已经丢失并删除其航迹？3.如果因内存或计算资源限制，现有航迹数量已经达到饱和，此时如果出现新的目标该如何处理？4.新建航迹时如何初始化各滤波器所需参数？ 

   航迹分成了四大类: ”Notconfirmed”、” Normal” 、”Noise”、 ”Lost”，其转换关系如下图所示：

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.226.png)

   图 40<a name="_toc27393"></a> 航迹转换关系示意图

   航迹被新建时，其被标识为”Notconfirmed”，此时我们还不确定它对应的目标是干扰/噪声产生的，还是正常的目标，当满足特定条件时，其会被转换成”Noise”或者”Normal”，标识为”noise”的航迹会被删除，只有标识为”Normal”的航迹其输出的结果才会被送入后端的应用模块，当满足一定条件后(目标从雷达视野中连续消失了很多帧)，认为目标已经不在探测范围内，此时将这条航迹标识为”Lost”并将之删除。

   1  <a name="_toc22363"></a>多节点融合数据生成
      1  <a name="_toc12298"></a>模型描述

         多节点融合数据生成模块包含多节点雷达侦察数据融合算法模型、多节点探测数据融合算法模型、多节点通信侦察数据融合算法模型三个子模型，通过这三个子模型实现多节点融合数据生成的功能需求。

      1  <a name="_toc1527"></a>实现设计
         1  <a name="_toc26869"></a>多节点雷达对抗侦察数据融合算法模型
            1  应用场景

               多站PDW融合定位技术广泛应用于以下领域：

1) 军用雷达侦察与对抗：定位敌方雷达或通信源，辅助电子干扰与反制。
1) 空域管理与预警：对入侵目标进行精准跟踪，提高防空反导反应速度。
1) 非合作目标监测：在未知雷达参数场景下实现隐蔽目标发现与追踪。
   1  总体思路

      系统采用分布式侦察站网络采集目标的脉冲描述字(PDW)数据，通过时间对齐、数据关联和融合处理，实现对运动辐射源的定位与跟踪。核心算法基于加权最小二乘优化，综合利用到达时间(TOA)和到达角(AOA)信息，同时估计目标的位置和速度。

   1  输入输出数据

      表 27<a name="_toc8053"></a> 输入数据

      |字段|类型|说明|
      | :-: | :-: | :-: |
      |Id|Int|目标编号|
      |Pos|vector<float>|位置|
      |Vel|vector<float>|速度|
      |Freq|Int|频率|
      |pw|double|脉宽|
      |prf|double|重频|
      |power|double|功率|
      |Time|double|时间戳|
      |Station|vector<float>|侦察站点及位置|

      表 28<a name="_toc1369"></a> 输出数据

      |字段|类型|说明|
      | :-: | :-: | :-: |
      |Time|double|时间戳|
      |Id|Int|目标编号|
      |Pos|vector<float>|位置|
      |Vel|vector<float>|速度|
      |improvement\_percentage|vector<float>|改善比例|
      |error\_distribution|vector<float>|误差分布|
      |fusion\_mean|float|融合平均值|
      |fusion\_std|float|融合标准差|

   1  流程图

      算法流程如[图 40](#_ref2401)所示：

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.227.png)

      <a name="_ref2401"></a>图 41<a name="_toc4791"></a> 多节点雷达对抗侦察数据融合算法模型流程图

   1  步骤详细说明
1) 数据加载与预处理
1) 数据加载：从json格式的输入文件读取PDW数据和元数据
1) 数据解析：将原始数据转换为PDW对象列表
1) 数据分组处理
1) 时间分组：将PDW数据按发射时间分配到离散的时间箱中
1  时间箱间隔由模拟更新间隔确定
1  采用最近邻原则分配PDW到时间箱
1) 目标分组：在每个时间箱内，按辐射源ID对PDW进行分组
1  确保同一目标的PDW被分配到同一组
1  记录参与侦察的站ID
1) 定位计算
1) 初始参数估计
1  位置初值：参与侦察站位置的平均值
1  速度初值：设为[0, 0]
1) 优化求解
1  构建残差函数，综合考虑TOA和AOA测量值
1  设置参数边界约束
1  调用最小二乘优化算法(TRF方法)
1) 结果提取
1  从优化结果中提取位置和速度估计
1  记录参考时间
1) 结果可视化
1) 基础绘图
1  绘制侦察站位置
1  绘制目标真实轨迹
1) 估计结果显示
1  绘制估计轨迹
1  标记估计点位置
1) 性能指标
1  计算并显示定位误差统计量
1  包括平均误差、最小误差和最大误差
   1  算法详细介绍

      PDW融合定位的核心在于利用多个站点对同一目标发射脉冲的观测差异来反推目标位置。这些差异主要包括时间差（TDOA）、频率差（FDOA）和角度差（AOA）。以下对各类方法的原理进行详细说明.

      1  TDOA定位（Time Difference of Arrival）

         TDOA是最常用的无源定位技术之一。多个侦察站接收到同一脉冲时，由于各站与目标之间的距离不同，导致脉冲到达时间存在差异。设站点i和j接收到的时间分别为TOAi与TOAj，则它们的TDOA为：\
         Δtij=TOAi−TOAj=di−dj/c

         其中![ref31]和![ref32]分别为目标到各站的距离，c为光速。对于N个站点，可形成N-1个TDOA观测量，在几何空间中构成多个双曲面，理论上三个站点即可在二维空间内精确定位目标。通过最小二乘法、迭代优化或卡尔曼滤波等方法可求解目标的估计位置。

      1  FDOA定位（Frequency Difference of Arrival)

         当目标处于运动状态时，其相对于各接收站的径向速度不同，引起多普勒频移差异。这种频率差异可用于目标运动方向估计。FDOA的基本公式如下：

         fij=fi−fj=(vi−vj)/λ

         其中fi和fj是站i和站j接收到的信号频率，υi和υj为目标相对于两个站的径向速度，λ为信号波长。FDOA与TDOA联合使用时，可同时估计目标的位置与速度。

      1  AOA定位（Angle of Arrival）

         部分接收站具备测角能力，可利用阵列天线或干涉仪估计DOA。多个DOA束在空间中交汇即为目标所在位置。AOA技术精度与阵列孔径、信噪比及测角算法密切相关，常作为TDOA定位的补充信息，用于提高定位精度和置信度。

      1  联合融合方法

         实际应用中，常将TDOA、FDOA和AOA联合建模，形成非线性定位方程组，通过非线性最小二乘、扩展卡尔曼滤波（EKF）、粒子滤波（PF）等估计算法，获得更高鲁棒性与精度。构建联合观测向量 

         z=Δtij(1),…,Δfij(2),…,θi(3),…T

         z=ℎ(x)+ν

         其中![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.230.png)为非线性观测函数，![ref33]为观测噪声。

         由于联合模型是非线性的，因此必须采用适当的非线性估计算法进行状态估计，主要采用了非线性最小二乘方法。其思路是将联合代价函数构造为各类观测误差平方和：	\
         J(x)=kzk−ℎkx2

         通过数值优化算法（如高斯-牛顿法、Levenberg-Marquardt）迭代求解最优状态估计![ref34]。

   1  <a name="_toc17311"></a>多节点雷达探测数据融合算法模型
      1  应用场景

         设计的核心应用场景是复杂电磁环境下的多源雷达数据融合，具体包含以下典型应用场景：

1) 军事防空应用

   在现代防空系统中，通常部署多部雷达组成探测网络，实现对同一目标的高效探测与跟踪。例如，远程警戒雷达负责大范围搜索，中程跟踪雷达负责精确跟踪，近程火控雷达负责武器引导。本系统可实现这些异构雷达数据的时空对齐和航迹融合，显著提高对隐身目标、低空突防目标的探测概率。实际部署时，需要考虑各雷达的布站几何（如三角部署可提高定位精度）、工作模式（旋转扫描/相控阵）和数据率差异。

1) 民航管制应用

   大型机场通常部署多部场面监视雷达和航路监视雷达。本系统可融合这些雷达数据，解决盲区覆盖问题，提高航班跟踪连续性。特别在复杂气象条件下，通过融合多部雷达数据可有效补偿单部雷达因天气导致的性能下降。

   1  总体思路

      多节点雷达探测融合系统的算法设计总体思路围绕"时空对齐-智能关联-最优融合"的技术路线展开，其核心是通过分层处理架构实现多部雷达数据的高效融合。系统首先建立统一的时空基准，将各雷达的球坐标观测数据转换为精度一致的笛卡尔坐标系数据。

      在航迹关联层面，设计了两级智能关联机制。粗关联阶段采用空间哈希和KD-Tree加速近邻搜索；精关联阶段创新性地提出动态门限马氏距离度量，综合考虑位置、速度、RCS等多维度特征，并引入交互多模型（IMM）预测结果作为关联约束。针对密集目标场景，算法自动切换至概率数据关联模式，通过建立可能性矩阵解决航迹交叉难题。关联过程特别设置了航向一致性校验和历史轨迹相似度分析等二级验证环节，有效降低虚假关联概率。

      状态融合环节采用最优加权估计算法，基于各雷达的测量误差协方差矩阵计算融合权重。推导过程中构建极大似然估计模型，通过Cholesky分解加速协方差矩阵求逆运算，最终得到最小方差无偏估计。针对非线性观测场景。系统实现层面采用多线程流水线设计，将处理流程分解为数据接收、时间对齐、坐标转换、航迹预测、数据关联、状态融合等并行环节。

      算法特别设计了三级异常处理机制：短期数据丢失采用Kalman预测补偿，中长期异常启动雷达健康诊断，系统级故障切换至降级模式运行。通过实时监控新息序列和残差统计量，动态调整关联门限和融合权重，有效增强了防空系统对目标的探测能力。

   1  输入输出数据

      表 29<a name="_toc511"></a> 输入数据

      |字段|类型|说明|
      | :-: | :-: | :-: |
      |Station|vector<float>|雷达站点及位置|
      |Time|double|时间戳|
      |SNR|double|信噪比|
      |Pos|vector<float>|位置|
      |Vel|vector<float>|速度|

      表 30<a name="_toc1279"></a> 输出数据

      |字段|类型|说明|
      | :-: | :-: | :-: |
      |Time|double|时间戳|
      |Pos|vector<float>|位置|
      |Vel|vector<float>|速度|
      |fused\_mean|float|平均误差|
      |fused\_median|float|中位数误差|
      |fused\_std|float|标准差|
      |Improvement\_per|float|融合性能提升百分比|
      |fused\_error|float|融合误差|

   1  流程图

      算法流程如[图 41](#_ref3175)所示：

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.233.png)

      <a name="_ref3175"></a>图 42<a name="_toc29854"></a> 多节点雷达探测数据融合算法模型流程图

   1  步骤详细说明
1) 数据输入 

   从JSON格式的输入文件读取多部雷达的点航迹等数据。

1) 坐标统一转换：时空基准统一
1) 时间同步

   时间对齐算法采用滑动窗口插值技术，对雷达扫描间隙进行数据补偿。具体实现中，系统维护一个时间缓冲区，当检测到某雷达数据缺失时，基于前后帧数据进行三次样条插值，确保时间连续性。

1) 空间坐标转换

   将各雷达的球坐标测量值(距离ρ、方位角θ、俯仰角φ)转换为局部笛卡尔坐标

1) 航迹预测与门限计算

   在完成时空对齐后，系统对现有航迹进行状态预测，并动态计算关联门限。

1) 航迹预测
1  初始化模型
1  模型概率自适应更新
1  卡尔曼预测状态向量
1) 门限计算
1  门限动态计算模型初始化
1  参数自适应
1) 两级智能关联
1) 粗关联
1  基于目标预测位置计算空间哈希值
1  仅对同一网格及相邻26个网格的航迹进行关联判断
1  采用KD-Tree优化近邻搜索
1) 精关联
1  航向一致性约束
1  历史轨迹形状相似性分析
1) 最优加权融合
   1) 协方差矩阵求逆：使用Cholesky分解加速运算
   1) 权重计算
   1) 状态融合
   1) 协方差更新
   1) 时间戳合成
1) 异常处理机制：误差评估+决策节点
   1) 数据级容错
1  短期丢失（≤3帧）：启动预测补偿
1  长期丢失：标记为"非激活"状态
1  异常值检测：马氏距离>5σ触发告警
   1) 全局监控
1  实时计算新息序列均值
1  动态调整过程噪声参数
1) 实时可视化：可视化输出
   1) 三维全局视图
1  航迹编码：融合航迹，单雷达航迹
   1) 极坐标视图
1  俯视图：XY平面投影，正北为0°
1  侧视图：XZ平面投影，正东为0°
1  动态标记：融合目标，单雷达目标
   1  算法详细介绍
      1  航迹关联算法

         航迹关联是将各节点雷达独立跟踪得到的局部目标航迹送往融合中心作关联处理，确定哪些航迹来自同一目标，以便进行后续航迹融合得到最终的航迹估计，而各节点雷达的航迹形成方法在之前的集中式融合结构有具体说明，这里不再赘述。本节主要研究K近邻域(K-NN)航迹关联算法，加权航迹关联算法和统计双门限关联算法。

1) k近领域关联

   假设送至融合中心的所有航迹均已完成时间配准和空间配准，这里以两部雷达为例说明，假设两部雷达的航迹号分别为：		\
   UA={1,2,⋯,nA},UB={1,2,⋯,nB}

   令来自节点雷达A 的航迹i 和节点雷达B 的航迹j 在k 时刻的状态估计差为		\
   Xij(k)=XiA(k|k)−XjB(k|k)=uij1,k,uij2,k,⋯,uijnx,k,(i∈UA,j∈UB)

   其中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.234.png)为节点雷达 A第 i 条航迹状态估计，![ref35]为节点雷达B 第j条航迹状态估计，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.236.png)是状态估计的维数。

   假设阈值向量为e=e1,e2,…,enxT，那么最近邻航迹关联算法的准则是：uij1,k<e1∩uij2,k<e2∩⋯∩uijnx,k<enx成立时，则认为节点雷达 A的航迹i 和节点雷达B 的航迹j 成功关联，如果出现多条航迹的状态估计满足上式的准则，则选择使![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.237.png)位置差范数最小的那条航迹作为最终成功关联的航迹。

   最近邻航迹关联算法优点是在稀疏目标环境下计算量小，简单而高效，适合应用于对实时性要求较高的环境。但是在密集目标环境下，会出现错误关联以及漏关联的现象。因此，为了解决NN算法在密集环境下的不足，提出了K近邻域(K-NN)航迹关联算法。

   取两个正整数![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.238.png)和![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.239.png)，其中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.240.png)；![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.241.png)；![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.238.png)是关联检验次数。那么，如果在![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.238.png)次关联检验中有K 次满足条件：

   Lij(k)(|uij1,k|<e1)∩(|uij2,k|<e2)∩⋯∩(|uijnx,k|<enx)i∈UA,j∈UB

   则认为两条航迹属于同一目标，也就是说，在0N 对航迹点中，只要至少存在K对航迹点落入关联域内，则认为两条航迹对应同一目标，这也是把算法称为K 近邻域算法(K-NN)的原因。

   K-NN 算法与NN 算法相比较，在密集目标环境下，关联性能获得了较大改善，但是K-NN 算法的不足是计算量比较大，不利于工程实现。

1) 加权航迹关联

   加权航迹关联算法是由Singer 等人提出来的，这种算法的前提条件是认为各雷达对同一个目标的状态估计误差之间是相对独立的。

   用XiA(k∣k)和XjB(k∣k)分别表示雷达A的第i条航迹和雷达B的第j条航迹在k时刻的真实值，XiAk∣k和XjB(k∣k)分别表示雷达 A的第 i 条航迹和雷达 B的第 j 条航迹在k 时刻的状态估计，对应的误差协方差矩阵分别为PiA(k∣k)和PjB(k∣k)，令\
   tijk=XiAk∣k−XjBk∣k\
   `	`设![ref36]和![ref37]为下列事件![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.244.png)

   ![ref36]：XiAk∣k和XjB(k∣k)为同一目标的航迹；

   ![ref37]：XiAk∣k和XjB(k∣k)为不同目标的航迹；

   利用假设检验的方法来判断两条航迹是否为同一个目标，该算法认为各雷达估计的误差是互相独立的，则在假设![ref36]下，两雷达的估计状态之差的协方差为: \
   Pijk∣k=Etijktijk'=EXiAk∣k−XjBk∣kXiAk∣k−XjBk∣k'=PiAk∣k+PjBk∣k

   采用的检验统计量为： 	

   αij(k)=XiAk | k−XjBk | kT⋅PiAk | k+PjBk | k−1∙XiAk|k−XjBk|k

   在![ref36]条件下，状态估计误差tij(k)=XiA(k∣k)−XjB(k∣k)服从高斯分布，则![ref38]服从![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.236.png)维的![ref39]分布，加权航迹关联算法认为如果![ref38]低于某一门限![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.247.png)，则认为![ref36]成立，成功关联航迹，也就是航迹i与航迹j来自于同一目标，否则接受假设![ref37]，视为关联失败。

   该算法的优点为：（1）相比仅利用航迹信息的检测统计量，加权法的检验统计量中利用了误差协方差，单次关联的效果更可靠；（2）实现起来简单。缺点为：对同一个目标，估计误差不一定总是满足高斯分布，而且将量测值的状态估计误差有所叠加，因此对于密集多目标环境中且存在交叉航迹，常出现漏关联现象，也可能会出现错误关联，针对此不足，利用双门限的思想，进一步研究统计双门限航迹关联算法。

1) 统计双门限航迹关联

   统计双门限航迹关联算法的基本思路是：对于两个雷达的R个估计误差，首先逐个基于![ref39]分布门限进行假设检验，若判为接受![ref36]，则计数加1，否则，计数的值不变，初始计数值为零，等所有点迹关联检验完成之后，把计数所累加数值与指定的数值L进行比较，经过R次假设检验之后，若计数器数值大于或等于L，则认为航迹关联成功，否则，认为关联失败，这里利用了两个门限判定，因此称其为统计双门限航迹关联方法。

   对于![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.248.png)，选用加权法的检验统计量，并逐个点迹计算 		

   βijAB(k)=XiA(k∣k)−XjB(k∣k)T⋅PiA(k∣k)+PjB(k∣k)−1∙XiAk∣k−XjBk∣k,(i∈UA,j∈UB)

   其中![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.249.png)、![ref35]、![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.250.png)、![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.251.png)分别表示雷达 A的第 i 条航迹和雷达 B的第 j 条航迹在k 时刻的状态估计和误差协方差矩阵。

   如果

   βijAB(k)≤δ

   这种利用加权法和双门限检测思想的算法称为统计双门限检测算法。

   1  航迹融合算法

      在分布式融合结构中，对成功关联的目标航迹对进行航迹融合，得到最终的航迹 估计。本节主要针对异步和同步数据两种情况，在异步情况下研究一种串行合并融合 算法，针对传统融合方法需要进行点迹的时间配准，此算法这样不仅不需要进行不同 雷达的点迹时间配准，而且可以大大提高融合之后的数据率。数据同步的情况下分析 了凸组合融合方法，并针对其局限性，研究一种快速 CI(covariance intersection)融合方法，这种算法高效的处理了不同雷达估计误差之间的相关性问题。

1) 串行合并融合算法

   由于雷达采样周期的不同，导致雷达对应量测点迹的时间信息可能不一致，我们 通常处理非同步采样的数据方法是采用之前介绍的时间对准的方法，将异步数据进行 时间配准，然后采用相应的融合算法进行航迹融合。但是我们也可以利用异步数据的 特点，对时间没有对准的点迹先进行串行合并，处理成类似单部雷达探测的点迹数据 流，再对合并后的航迹进行滤波处理。这种方法被称为串行合并融合，其基本步骤如[图 42](#_ref11697)所示：

   ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.252.png)

   <a name="_ref11697"></a>图 43<a name="_toc3255"></a> 串行合并流程

   由上图可以看到，串行合并方法好处在于增加了整个多站雷达系统的数据率，提 高了跟踪的精度，适用于目标运动变化较快的情况，另外整体数据率增大，也大大加 快了航迹起始速度，有助于解决突发情况，在整个点迹合并处理之后，就可以通过卡 尔曼滤波算法进行滤波完成航迹融合。

1) 凸组合融合算法

   凸组合算法对于数据同步情况下的航迹融合较为高效，也易于实现，因此得到广 泛应用。但算法的前提条件是认为不同雷达对同一目标的局部估计误差互不相关，也 就是说这种算法在互协方差为零时性能最优。

   算法融合处理公式为： 

   Pk|k=(i=1N(Pk|kj)−1)−1

   Xk|k=Pk|ki=1NPk|ki−1Xk|k\
	

   其中，N 为多站雷达系统中的节点雷达个数，Xkk是第 i部雷达在 k时刻对目标的状态估计（包括位置数据和速度数据），Pk|ki是其 k时刻状态估计对应的误差协方差矩阵。Xk|k为融合后的状态估计，Pk|k为融合后的状态估计的误差协方差矩阵。

1) 快速 CI 融合算法

   针对凸组合融合方法的局限性，CI融合算法可以很好地处理不同雷达对同一目 标估计误差的互相关未知问题。假设两部雷达对同一目标的局部状态估计为(X1,P1)和(X2,P2)，其中，P1，P2分别为目标状态估计 ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.253.png),![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.254.png)的误差协方差。根据CI 融合准则，得到融合后状态估计(XCI,PCI)为: 

   XCI=PCI(ω1P1−1X1+ω2P2−1X2)	

   PCI=(W1P1−1+W2P2−1)−1

   其中，![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.255.png). 为相应局部状态估计的权值，应满足下式:\
   W1+W2=1

   可以看到求解权值的过程是该算法的关键，不同的求解方法对应多种广义 CI 融合算法，其中，行列式最小 CI 融合算法的权值求解方法为：\
   min|PCI|=minW1P1−1+W2P2−1−1

   由上式可以看出，该权值求解过程是一个非线性问题，计算量较大。因此，为了加快航迹融合的效率，满足系统实时性要求，这里研究一种快速CI融合(Improved fast covariance intersection, IFCI)，它的权值求解方法如下：\
   ω1=P1+P2−P2+P12P1+P2

   可见，IFCI 算法是行列式最小 CI 算法的一种近似，虽然相比精度会有相应的损失，但也只是在各雷达对同一目标局部估计误差高度相关的情况下，而 IFCI 算法可 大大提升计算速率，提高融合效率。

   1  <a name="_toc21218"></a>多节点通信对抗侦察数据融合
      1  应用场景
1) <a name="ole_link46"></a>应用场景
   1. 军事侦察：对敌方通信设备、雷达辐射源等目标进行定位跟踪。

      在<a name="ole_link56"></a>多战域协同作战环境下，通信侦察数据融合算法可实现对敌方通信设备、雷达辐射源等电磁目标的高精度定位与连续跟踪。例如，通过融合部署于地面侦测站、电子侦察机及无人平台的通信侦察数据，可实时解算敌方指挥电台、数据链终端等关键目标的三维空间位（TDOA/FDOA定位），同时关联目标身份参数（如跳频模式、调制特征）。在实战中，能够<a name="ole_link48"></a>显著提升对敌机动指挥车、导弹阵地等目标的动态监控效能，并为电子对抗（如定向干扰）提供实时目标指示。部署时需优化多平台协同拓扑，兼顾隐蔽性与数据实时性需求。

   1. 电子情报收集：对辐射源目标进行特征分析和定位。
1) 总体思路

   系统通过多个分散部署的接收站异步采集目标辐射信号，经信号预处理<a name="ole_link61"></a>（频段提取、归一化）和多目标分离（特征分解、主信号提取）后，并行计算TDOA、FD<a name="ole_link64"></a>OA和AOA三类关键观测参数；基于这些异构特征构建包含目标位置-速度状态向量的<a name="ole_link66"></a>扩展卡尔曼滤波器(EKF)模型：<a name="ole_link63"></a>以接收站几何中心为初始位置、零速度向量为初值，通过匀速运动模型进行状态预测；在观测更新阶段，将非线性TDOA/ FDOA / AOA测量方程线性化并构建雅可比矩阵，创新融合自适应噪声加权（依据信噪比动态调整观测噪声）与带约束优化（位置边界约束+TRF最小二乘优化），实现对目标位置和速度的高精度联合估计；最终通过轨迹可视化与误差统计验证算法在复杂电磁环境下的鲁棒性。

   1  总体思路

      系统通过多个分散部署的接收站采集目标辐射信号，通过信号处理提取关键特征参数，最后通过扩展卡尔曼滤波器(EKF)融合这些观测数据实现对目标位置和速度的联合估计。

   1  输入输出数据

      表 31<a name="_toc11058"></a> 输入数据

      |字段|类型|说明|
      | :-: | :-: | :-: |
      |Station|vector<float>|通信侦察站点及位置|
      |Time|double|时间戳|
      |Id|Int|目标编号|
      |Pos|vector<float>|位置|
      |Vel|vector<float>|速度|
      |Freq|Int|频率|
      |Mode\_Type|Char|调制类型|

      表 32<a name="_toc19531"></a> 输出数据

      |字段|类型|说明|
      | :-: | :-: | :-: |
      |Time|double|时间戳|
      |Mode\_Type|Char|调制类型|
      |Id|Int|目标编号|
      |Pos|vector<float>|位置|
      |Vel|vector<float>|速度|
      |fused\_pos\_error|float|位置误差|
      |Fused\_vel\_error|float|速度误差|

   1  流程图

      算法流程如[图 43](#_ref12141)所示：

      ![](Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.256.png)

      <a name="_ref12141"></a>图 44<a name="_toc16159"></a> 多节点通信对抗侦察数据融合算法模型流程图

   1  步骤详细说明
1) <a name="ole_link59"></a>数据<a name="ole_link47"></a>加载与预处理
1) 数据加载：从JSON格式的输入文件读取数据
1) 数据解析：将原始数据转换为对象列表
1) 信号特征提取
1) 信号预处理
1  获取目标信号频段
1  信号归一化
1) 多目标信号分离
1  信号特征分解
1  主信号分离
1) TDOA精确估计
1) FDOA精确估计
1) AOA精确估计
1) EKF融合定位
1) 初始参数估计
1  位置初值：参与接收站位置的平均值
1  速度初值：设为[0, 0]
1) 状态预测
1  构建残差函数，综合考虑TOA和AOA测量值
1  设置参数边界约束
1  调用最小二乘优化算法(TRF方法)
1) 观测预测
1) 卡尔曼增益
1) 状态更新
1) 结果可视化
1) 基础绘图
1  绘制接收站位置
1  绘制目标真实轨迹
1) 估计结果显示
1  绘制估计轨迹
1  标记估计点位置
1) 性能指标
1  计算并显示定位误差统计量
1  包括平均误差、最小误差和最大误差
   1  算法详细介绍

      通信侦察信号融合定位的核心在于利用多个站点对同一目标发射脉冲的观测差异来反推目标位置。这些差异主要包括时间差（TDOA）、频率差（FDOA）和角度差（AOA）。以下对各类方法的原理进行详细说明.

      1  TDOA定位（Time Difference of Arrival）

         TDOA是最常用的无源定位技术之一。多个侦察站接收到同一脉冲时，由于各站与目标之间的距离不同，导致脉冲到达时间存在差异。设站点i和j接收到的时间分别为TOAi与TOAj，则它们的TDOA为：	\
         Δtij=TOAi−TOAj=di−dj/c

         其中![ref31]和![ref32]分别为目标到各站的距离，c为光速。对于N个站点，可形成N-1个TDOA观测量，在几何空间中构成多个双曲面，理论上三个站点即可在二维空间内精确定位目标。通过最小二乘法、迭代优化或卡尔曼滤波等方法可求解目标的估计位置。

      1  FDOA定位（Frequency Difference of Arrival）

         当目标处于运动状态时，其相对于各接收站的径向速度不同，引起多普勒频移差异。这种频率差异可用于目标运动方向估计。FDOA的基本公式如下：\
         fij=fi−fj=(vi−vj)/λ

         其中fi和fj是站i和站j接收到的信号频率，vi和vj为目标相对于两个站的径向速度，λ为信号波长。FDOA与TDOA联合使用时，可同时估计目标的位置与速度。

      1  AOA定位（Angle of Arrival）

         部分接收站具备测角能力，可利用阵列天线或干涉仪估计DOA。多个DOA束在空间中交汇即为目标所在位置。AOA技术精度与阵列孔径、信噪比及测角算法密切相关，常作为TDOA定位的补充信息，用于提高定位精度和置信度。

      1  <a name="ole_link65"></a>联合融合方法

         实际应用中，常将TDOA、FDOA和AOA联合建模，形成非线性定位方程组，通过非线性最小二乘、扩展卡尔曼滤波（EKF）、粒子滤波（PF）等估计算法，获得更高鲁棒性与精度。构建联合观测向量

         z=Δtij(1),…,Δfij(2),…,θi(3),…T

         z=ℎ(x)+ν

         其中ℎ(⋅)为非线性观测函数，![ref33]为观测噪声。

         由于联合模型是非线性的，因此必须采用适当的非线性估计算法进行状态估计，主要采用了非线性最小二乘方法。其思路是将联合代价函数构造为各类观测误差平方和：

         J(x)=kzk−ℎkx2

         通过数值优<a name="ole_link67"></a>化算法（如高斯-牛顿法、Levenberg-Marquardt）迭代求解最优状态估计![ref34]。

      1  扩展卡尔曼滤波器

         扩展卡尔曼滤波(Extended Kalman Filter)是建立在经典卡尔曼滤波算法的基础上。核心思想是对非线性系统，首先围绕滤波值X(k)将非线性函数 f(∗) 和 h(∗) 展开成[Taylor级数](https://zhida.zhihu.com/search?content_id=246288655&content_type=Article&match_order=1&q=Taylor%E7%BA%A7%E6%95%B0&zhida_source=entity)但只保存一阶及以下部分（舍去二阶及以上项），得到近似的线性化模型。然后利用[kalman滤波](https://zhida.zhihu.com/search?content_id=246288655&content_type=Article&match_order=1&q=kalman%E6%BB%A4%E6%B3%A2&zhida_source=entity)算法完成对目标的滤波估计等处理。

         <a name="ole_link68"></a>(1) 局部线性化   

         离散非线性系统动态方程可以表示为：

         X(k+1)=f[k,X(k)]+G(k)W(k)Z(k)=ℎ[k,X(k)]+V(k)

         由系统状态方程，将非线性函数f(∗)围绕滤波值值X(k)做一阶Taylor展开：

         X(k+1)≈f[k,X(k)]+∂f∂X(k)[X(k)−X(k)]+G[X(k),k]W(k)

         令：

         ∂f∂X(k)=∂f[X(k),k]∂X(k)|X(k)=X(k)=Φ(k+1∣k)

         ∂f[X(k),k]−∂f∂X(k)|X(k)=X(k)X(k)=ϕ(k)

         则状态方程为：

         X(k+1)=Φ(k+1∣k)X(k)+G(k)W(k)+ϕ(k)

         由系统状态方程，将非线性函数h(\*)围绕滤波值X(k)做一阶Taylor展开，得：

         Zk=ℎXk∣k−1,k+∂ℎ∂Xk|Xk,k−1Xk−Xk∣k−1+Vk

         令：∂ℎ∂X(k)|X(k)=X(k)=H(k)

         y(k)=ℎ[X(k)k−1,k]−∂ℎ∂X(k)|X(k)=X(k)X(k∣k−1)

         则观测方程为：

         Z(k)=H(k)X(k)+y(k)+V(k)

         (2) EKF算法

         扩<a name="ole_link70"></a>展卡尔曼滤波（Extended Kalman Filter）是<a name="ole_link71"></a>非线性系统状态估计的实施标准，在状态转移方程确定的情况下，扩展卡尔曼滤波处理非线性问题的主要方法是泰勒展开，求非线性函数的雅可比矩阵。非线性问题一般存在于预测和观测过程，分别对这两部分求雅可比矩阵，作为卡尔曼滤波中的预测矩阵和观测矩阵。

         ① 首先定义状态转换方程和观测方程：

         Xk=f(Xk−1,uk)+wk

         其中，Xk是k时刻的状态向量， f是状态转移函数，uk是控制输入，wk是过程噪声，服从均值为0、协方差为Qk的高斯分布，即wk∼N(0,Qk)。

         在目标跟踪中，我们通常采用匀速模型（CV模型），因此，状态方程简化为线性，观测方程为非线性：

         Zk=ℎ(Xk)+vk,  vk∼N(0,Rk)

         其中，h是非线性观测函数，vk是观测噪声，服从均值为0、协方差为Rk的高斯分布，Rk观测噪声协方差矩阵。

         ② 初始状态估计：X0|0；初始误差协方差：P00

         ③ 进行状态预测和协方差预测：

         Xk|k−1=f(Xk−1|k−1)Pk|k−1=Fk−1Pk−1|k−1Fk−1T+Qk−1

         其中F为状态转移雅可比矩阵：

         Fk−1=∂f∂XXk−1k−1

         ④ 更新步骤：

         a. 观测状态残差计算：         yk=Zk−ℎ(Xk|k−1)

         b. 观测雅可比矩阵：           Hk=∂ℎ∂XXk−1k−1

         c. 卡尔曼增益计算：          Kk=Pk|k−1HkT(HkPk|k−1HkT+Rk)−1

         <a name="ole_link74"></a>d. 状态更新：                Xk|k−1=Xk−1|k−1+Kkyk

         e. 协方差更新：              Pk|k=(I−KkHk)Pk|k−1

         ⑤ 重复步骤3-4进行时间递推,k=k+1.

         其中，状态转移函数（匀速模型）：

         f(X)=x+vxty+vytvxvy,     F=10Δt0010Δt00100001

         观测雅可比矩阵（TDOA/FDOA/AOA联合）：

         H=∂τij/∂x∂τij/∂y00∂fdij/∂x∂fdij/∂y∂fdij/∂νx∂fdij/∂νy∂θm/∂x∂θm/∂y00

         其中：

         ∂τij∂x=1cx−sixri−x−sjxrj;∂fdij∂νx=f0cx−sixri−x−sjxrj;∂θm∂x=−y−smyrm2

   1  <a name="_toc2245"></a>注释

      无。
2

[ref1]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.002.png
[ref2]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.003.png
[ref3]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.015.png
[ref4]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.022.png
[ref5]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.030.png
[ref6]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.033.png
[ref7]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.034.png
[ref8]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.079.png
[ref9]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.086.png
[ref10]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.112.png
[ref11]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.113.png
[ref12]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.128.png
[ref13]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.137.png
[ref14]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.175.png
[ref15]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.176.png
[ref16]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.177.png
[ref17]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.183.png
[ref18]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.185.png
[ref19]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.186.png
[ref20]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.187.png
[ref21]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.188.png
[ref22]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.192.png
[ref23]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.196.png
[ref24]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.203.png
[ref25]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.205.png
[ref26]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.212.png
[ref27]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.213.png
[ref28]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.215.png
[ref29]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.217.png
[ref30]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.224.png
[ref31]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.228.png
[ref32]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.229.png
[ref33]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.231.png
[ref34]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.232.png
[ref35]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.235.png
[ref36]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.242.png
[ref37]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.243.png
[ref38]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.245.png
[ref39]: Aspose.Words.3642dafd-732b-473d-a797-0a78115402ca.246.png
