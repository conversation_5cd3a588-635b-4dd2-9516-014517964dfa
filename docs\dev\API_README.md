# 光电对抗仿真系统API文档

## 概述

`api.py` 提供了光电对抗仿真系统的函数式API接口，允许用户通过传入JSON配置字典来执行仿真，并返回完整的输出文件夹路径结构和仿真详细信息。

## 主要功能

- **函数式接口**: 通过 `run_simulation_api()` 函数执行仿真
- **完整输出信息**: 返回详细的文件结构、性能指标和仿真结果
- **配置验证**: 自动验证配置参数的有效性
- **多种模板**: 提供预设的配置模板
- **错误处理**: 完善的异常处理和错误报告
- **会话隔离**: 每次调用创建独立的输出目录

## 核心API函数

### `run_simulation_api(config_input, output_base_dir=None, log_level='INFO', num_threads=None)`

执行光电对抗仿真的主要函数。

**参数:**
- `config_input`: 仿真配置，支持字典类型或JSON字符串
- `output_base_dir`: 输出基础目录路径（可选）
- `log_level`: 日志级别，可选值: DEBUG, INFO, WARNING, ERROR
- `num_threads`: 并行线程数（可选，自动检测）

**返回值:**
返回JSON字符串格式，解析后包含以下信息的字典：
```python
{
    "success": bool,                    # 仿真是否成功
    "session_info": {                   # 会话信息
        "session_id": str,              # 会话ID
        "start_time": str,              # 开始时间
        "end_time": str,                # 结束时间
        "duration": float,              # 执行时长(秒)
        "output_directory": str         # 输出目录完整路径
    },
    "simulation_config": {              # 仿真配置摘要
        "scenario_name": str,           # 场景名称
        "duration": float,              # 仿真时长
        "data_count": int,              # 数据生成数量
        "output_types": List[str],      # 输出类型
        "device_count": Dict[str, int]  # 各类设备数量
    },
    "output_structure": {               # 输出文件夹完整结构
        "total_files": int,             # 文件总数
        "total_size": int,              # 总大小(字节)
        "categories": {                 # 各类别文件信息
            "images": {...},
            "videos": {...},
            "data": {...},
            "logs": {...},
            "configs": {...}
        },
        "directory_tree": {...}         # 目录树结构
    },
    "simulation_results": {             # 仿真结果文件路径
        "images": List[str],            # 图像文件路径列表
        "videos": List[str],            # 视频文件路径列表
        "data": List[str],              # 数据文件路径列表
        "summary": List[str]            # 摘要文件路径列表
    },
    "performance_metrics": {            # 性能指标
        "processing_speed": float,      # 处理速度(数据/秒)
        "memory_usage": Dict[str, Any], # 内存使用情况
        "execution_efficiency": {...}   # 执行效率指标
    },
    "error_info": Optional[str]         # 错误信息(如果失败)
}
```

## 辅助函数

### `validate_config_dict(config_dict)`
验证配置字典的基本结构和必要字段。

### `get_default_config()`
获取默认配置模板。

### `get_config_template(template_name)`
获取指定的配置模板，支持的模板：
- `"basic"`: 基础配置
- `"advanced"`: 高级配置（包含多种设备）
- `"bad_weather"`: 恶劣天气场景配置

### `create_simulation_summary(result)`
创建仿真结果的格式化文本摘要。

## 新功能特性 (v2.0)

### 多种输入格式支持
- **字典输入**: 直接传入Python字典对象
- **JSON字符串输入**: 传入JSON格式的字符串

### JSON字符串输出
- 统一返回JSON字符串格式，便于序列化和网络传输
- 使用 `json.loads()` 解析返回结果

### 可选字段容错处理
- `optical_jammers`、`optical_recons` 等可选字段不存在时不会报错
- 自动为缺失的可选字段设置空列表默认值
- 提高配置的灵活性和容错性

## 配置结构

配置字典应包含以下主要部分：

```python
{
    "simulation": {
        "scenario_name": "场景名称",
        "duration": 60.0,              # 仿真时长(秒)
        "time_step": 0.1,              # 时间步长(秒)
        "data_count": 100,             # 数据生成数量
        "output_types": ["static_images", "parameters"],
        "environment": {               # 环境参数
            "weather_condition": "clear_weather",
            "temperature": 288.15,
            "humidity": 0.6,
            # ... 其他环境参数
        }
    },
    "system": {
        "max_threads": 4,
        "image_resolution": [640, 480],
        "video_fps": 30,
        "random_seed": None
    },
    "optical_targets": [               # 光电目标设备列表
        {
            "model": "目标型号",
            "position": {
                "latitude": 39.9042,
                "longitude": 116.4074,
                "altitude": 1000.0
            },
            "performance_params": {
                "detection_range": 5000,
                "resolution": 0.1,
                # ... 其他性能参数
            },
            # ... 其他配置
        }
    ],
    "optical_jammers": [...],          # 光电干扰设备列表（可选）
    "optical_recons": [...]            # 光电侦察设备列表（可选）
}
```

## 使用示例

### 基本使用 - 字典输入

```python
import json
from api import run_simulation_api, get_default_config

# 获取默认配置
config = get_default_config()

# 执行仿真（字典输入，JSON字符串输出）
result_json = run_simulation_api(config_input=config)

# 解析JSON结果
result = json.loads(result_json)

if result["success"]:
    print(f"仿真成功! 输出目录: {result['session_info']['output_directory']}")
    print(f"生成文件数: {result['output_structure']['total_files']}")
else:
    print(f"仿真失败: {result['error_info']}")
```

### JSON字符串输入

```python
import json
from api import run_simulation_api

# JSON字符串配置
config_json = '''
{
    "simulation": {
        "scenario_name": "JSON输入示例",
        "duration": 30.0,
        "data_count": 20,
        "output_types": ["parameters"],
        "environment": {
            "weather_condition": "clear_weather",
            "temperature": 288.15,
            "humidity": 0.6
        }
    },
    "system": {
        "max_threads": 2,
        "image_resolution": [640, 480]
    },
    "optical_targets": [
        {
            "model": "示例目标",
            "position": {"latitude": 39.9042, "longitude": 116.4074, "altitude": 1000.0},
            "observation_direction": {"azimuth": 0.0, "elevation": 0.0},
            "performance_params": {"detection_range": 5000, "resolution": 0.1},
            "work_mode": "passive_search"
        }
    ]
}
'''

# 执行仿真（JSON字符串输入和输出）
result_json = run_simulation_api(config_input=config_json)
result = json.loads(result_json)

print(f"仿真结果: {'成功' if result['success'] else '失败'}")
```

### 使用预设模板

```python
from api import run_simulation_api, get_config_template

# 使用恶劣天气模板
config = get_config_template("bad_weather")

# 自定义修改
config["simulation"]["data_count"] = 50

# 执行仿真
result_json = run_simulation_api(
    config_input=config,
    output_base_dir="./my_results",
    log_level="DEBUG"
)

# 解析结果
result = json.loads(result_json)
```

### 完全自定义配置

```python
from api import run_simulation_api, validate_config_dict

# 创建自定义配置
custom_config = {
    "simulation": {
        "scenario_name": "自定义场景",
        "duration": 30.0,
        "data_count": 20,
        "output_types": ["static_images", "parameters"],
        "environment": {
            "weather_condition": "clear_weather",
            "temperature": 288.15,
            "humidity": 0.6
        }
    },
    "system": {
        "max_threads": 2,
        "image_resolution": [800, 600]
    },
    "optical_targets": [
        {
            "model": "自定义目标",
            "position": {
                "latitude": 40.0,
                "longitude": 116.0,
                "altitude": 1000.0
            },
            "performance_params": {
                "detection_range": 5000,
                "resolution": 0.1
            },
            "work_mode": "passive_search"
        }
    ]
}
# 注意：没有包含 optical_jammers 和 optical_recons 字段，这是允许的

# 验证配置
is_valid, errors = validate_config_dict(custom_config)
if not is_valid:
    print("配置无效:", errors)
else:
    # 执行仿真
    result_json = run_simulation_api(config_input=custom_config)
    result = json.loads(result_json)
```

### 可选字段容错示例

```python
import json
from api import run_simulation_api

# 最小配置 - 只包含必要字段，不包含可选的optical字段
minimal_config = {
    "simulation": {
        "scenario_name": "最小配置示例",
        "duration": 10.0,
        "data_count": 5,
        "output_types": ["parameters"],
        "environment": {
            "weather_condition": "clear_weather",
            "temperature": 288.15,
            "humidity": 0.6
        }
    },
    "system": {
        "max_threads": 2,
        "image_resolution": [640, 480]
    },
    "optical_targets": [
        {
            "model": "基础目标",
            "position": {"latitude": 39.9042, "longitude": 116.4074, "altitude": 1000.0},
            "observation_direction": {"azimuth": 0.0, "elevation": 0.0},
            "performance_params": {"detection_range": 5000, "resolution": 0.1},
            "work_mode": "passive_search"
        }
    ]
    # 注意：没有 optical_jammers 和 optical_recons 字段
}

# 执行仿真 - 缺失的可选字段会自动设置为空列表
result_json = run_simulation_api(config_input=minimal_config)
result = json.loads(result_json)

if result["success"]:
    # 查看设备统计，可选字段会显示为0
    print("设备统计:", result['simulation_config']['device_count'])
    # 输出: {'optical_targets': 1, 'optical_jammers': 0, 'optical_recons': 0}
```

## 输出文件结构

每次仿真会在输出目录下创建以下结构：

```
simulation_results/
└── session_YYYYMMDD_HHMMSS_mmm/
    ├── images/          # 生成的图像文件
    ├── videos/          # 生成的视频文件
    ├── data/            # 参数数据文件
    ├── logs/            # 日志文件
    └── configs/         # 配置文件备份
```

## 性能优化建议

1. **合理设置数据数量**: `data_count` 参数直接影响执行时间
2. **调整线程数**: 根据CPU核心数设置 `num_threads`
3. **选择必要的输出类型**: 只生成需要的数据类型
4. **使用适当的图像分辨率**: 高分辨率会增加处理时间

## 错误处理

API提供完善的错误处理机制：

- 配置验证错误会在执行前被捕获
- 运行时错误会被记录到日志文件
- 错误信息会保存到输出目录的 `logs/error_report.json`
- 返回结果中包含详细的错误信息

## 注意事项

1. 确保系统有足够的磁盘空间存储输出文件
2. 大量数据生成可能需要较长时间，请耐心等待
3. 每次调用都会创建新的会话目录，避免文件冲突
4. 建议定期清理旧的输出目录以节省磁盘空间

## 完整示例

参见以下示例文件：
- `api_usage_examples.py` - 展示新功能的完整示例代码

这些文件包含了字典输入、JSON字符串输入、可选字段处理等各种使用场景的完整示例。
