"""
仿真引擎模块
负责协调各个仿真模块的执行，管理多线程处理
"""

import logging
import threading
import concurrent.futures
from typing import Dict, List, Any, Optional
import numpy as np
from datetime import datetime

from .config_manager import ConfigManager
from .output_manager import OutputManager
from ..devices.optical_target import OpticalTargetSimulator
from ..devices.optical_jammer import OpticalJammerSimulator
from ..devices.optical_recon import OpticalReconSimulator
from ..utils.logger import LoggerMixin, log_execution_time


logger = logging.getLogger(__name__)


class SimulationEngine(LoggerMixin):
    """仿真引擎"""
    
    def __init__(
        self,
        config_manager: Config<PERSON>ana<PERSON>,
        output_manager: OutputManager,
        num_threads: Optional[int] = None
    ):
        """
        初始化仿真引擎
        
        Args:
            config_manager: 配置管理器
            output_manager: 输出管理器
            num_threads: 线程数，None表示自动检测
        """
        self.config_manager = config_manager
        self.output_manager = output_manager
        
        # 设置线程数
        if num_threads is None:
            import os
            self.num_threads = min(os.cpu_count() or 4, config_manager.system.max_threads)
        else:
            self.num_threads = min(num_threads, config_manager.system.max_threads)
        
        # 初始化仿真器
        self.target_simulators = []
        self.jammer_simulators = []
        self.recon_simulators = []
        
        self._initialize_simulators()
        
        # 设置随机种子
        if config_manager.system.random_seed is not None:
            np.random.seed(config_manager.system.random_seed)
        
        self.logger.info(f"仿真引擎初始化完成，使用 {self.num_threads} 个线程")
    
    def _initialize_simulators(self):
        """初始化各类仿真器"""
        # 初始化光电目标仿真器
        for i, target_config in enumerate(self.config_manager.optical_targets):
            simulator = OpticalTargetSimulator(
                config=target_config,
                system_config=self.config_manager.system,
                environment=self.config_manager.simulation.environment
            )
            self.target_simulators.append(simulator)
            self.logger.debug(f"初始化光电目标仿真器 {i}: {target_config.model}")
        
        # 初始化光电干扰仿真器
        for i, jammer_config in enumerate(self.config_manager.optical_jammers):
            simulator = OpticalJammerSimulator(
                config=jammer_config,
                system_config=self.config_manager.system,
                environment=self.config_manager.simulation.environment
            )
            self.jammer_simulators.append(simulator)
            self.logger.debug(f"初始化光电干扰仿真器 {i}: {jammer_config.model}")
        
        # 初始化光电侦察仿真器
        for i, recon_config in enumerate(self.config_manager.optical_recons):
            simulator = OpticalReconSimulator(
                config=recon_config,
                system_config=self.config_manager.system,
                environment=self.config_manager.simulation.environment
            )
            self.recon_simulators.append(simulator)
            self.logger.debug(f"初始化光电侦察仿真器 {i}: {recon_config.model}")
    
    @log_execution_time
    def run(self) -> Dict[str, List[str]]:
        """
        执行仿真
        
        Returns:
            仿真结果文件路径字典
        """
        self.logger.info("开始执行仿真")
        
        results = {
            'images': [],
            'videos': [],
            'data': [],
            'summary': []
        }
        
        try:
            # 并行执行各类设备仿真
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.num_threads) as executor:
                futures = []
                
                # 提交光电目标仿真任务
                for i, simulator in enumerate(self.target_simulators):
                    future = executor.submit(self._run_target_simulation, simulator, i)
                    futures.append(('target', i, future))
                
                # 提交光电干扰仿真任务
                for i, simulator in enumerate(self.jammer_simulators):
                    future = executor.submit(self._run_jammer_simulation, simulator, i)
                    futures.append(('jammer', i, future))
                
                # 提交光电侦察仿真任务
                for i, simulator in enumerate(self.recon_simulators):
                    future = executor.submit(self._run_recon_simulation, simulator, i)
                    futures.append(('recon', i, future))
                
                # 收集结果
                for device_type, device_id, future in futures:
                    try:
                        device_results = future.result()
                        self._merge_results(results, device_results)
                        self.logger.info(f"{device_type} {device_id} 仿真完成")
                    except Exception as e:
                        self.logger.error(f"{device_type} {device_id} 仿真失败: {e}")
            
            # 生成综合分析结果
            if self.target_simulators and self.jammer_simulators:
                analysis_results = self._run_interference_analysis()
                self._merge_results(results, analysis_results)
            
            # 生成仿真摘要
            summary_path = self.output_manager.create_summary_report(results)
            results['summary'].append(summary_path)
            
            self.logger.info("仿真执行完成")
            return results
            
        except Exception as e:
            self.logger.error(f"仿真执行失败: {e}")
            raise
    
    def _run_target_simulation(self, simulator: OpticalTargetSimulator, device_id: int) -> Dict[str, List[str]]:
        """执行光电目标仿真"""
        self.logger.debug(f"开始光电目标 {device_id} 仿真")
        
        results = {'images': [], 'videos': [], 'data': []}
        
        # 生成静态图像
        if 'static_images' in self.config_manager.simulation.output_types:
            image_paths = simulator.generate_static_images(
                count=self.config_manager.simulation.data_count,
                output_manager=self.output_manager,
                device_id=device_id
            )
            results['images'].extend(image_paths)
        
        # 生成动态图像/视频
        if 'dynamic_images' in self.config_manager.simulation.output_types:
            video_paths = simulator.generate_dynamic_images(
                duration=self.config_manager.simulation.duration,
                output_manager=self.output_manager,
                device_id=device_id
            )
            results['videos'].extend(video_paths)
        
        # 生成参数数据
        if 'parameters' in self.config_manager.simulation.output_types:
            param_paths = simulator.generate_parameter_data(
                count=self.config_manager.simulation.data_count,
                output_manager=self.output_manager,
                device_id=device_id
            )
            results['data'].extend(param_paths)
        
        return results
    
    def _run_jammer_simulation(self, simulator: OpticalJammerSimulator, device_id: int) -> Dict[str, List[str]]:
        """执行光电干扰仿真"""
        self.logger.debug(f"开始光电干扰 {device_id} 仿真")
        
        results = {'images': [], 'videos': [], 'data': []}
        
        # 生成干扰参数数据
        param_paths = simulator.generate_jamming_data(
            count=self.config_manager.simulation.data_count,
            output_manager=self.output_manager,
            device_id=device_id
        )
        results['data'].extend(param_paths)
        
        return results
    
    def _run_recon_simulation(self, simulator: OpticalReconSimulator, device_id: int) -> Dict[str, List[str]]:
        """执行光电侦察仿真"""
        self.logger.debug(f"开始光电侦察 {device_id} 仿真")
        
        results = {'images': [], 'videos': [], 'data': []}
        
        # 生成侦察数据
        recon_paths = simulator.generate_reconnaissance_data(
            count=self.config_manager.simulation.data_count,
            output_manager=self.output_manager,
            device_id=device_id
        )
        results['data'].extend(recon_paths)
        
        return results
    
    def _run_interference_analysis(self) -> Dict[str, List[str]]:
        """执行干扰效果分析"""
        self.logger.debug("开始干扰效果分析")
        
        results = {'data': []}
        
        # 分析光电目标与干扰设备的相互作用
        analysis_data = []
        
        for target_sim in self.target_simulators:
            for jammer_sim in self.jammer_simulators:
                # 计算干扰效果
                interference_effect = self._calculate_interference_effect(target_sim, jammer_sim)
                analysis_data.append(interference_effect)
        
        if analysis_data:
            # 保存分析结果
            analysis_path = self.output_manager.save_json_data(
                analysis_data, 'data', 'interference_analysis'
            )
            results['data'].append(analysis_path)
        
        return results
    
    def _calculate_interference_effect(self, target_sim, jammer_sim) -> Dict[str, Any]:
        """计算干扰效果"""
        # 计算距离
        target_pos = target_sim.config.position
        jammer_pos = jammer_sim.config.position
        
        distance = np.sqrt(
            (target_pos['latitude'] - jammer_pos['latitude'])**2 +
            (target_pos['longitude'] - jammer_pos['longitude'])**2 +
            (target_pos['altitude'] - jammer_pos['altitude'])**2
        )
        
        # 简化的干扰效果计算
        jammer_power = jammer_sim.config.performance_params.get('jamming_power', 100)
        interference_ratio = jammer_power / (distance**2 + 1)  # 简化的功率衰减模型
        
        return {
            'target_model': target_sim.config.model,
            'jammer_model': jammer_sim.config.model,
            'distance': distance,
            'interference_ratio': interference_ratio,
            'timestamp': datetime.now().isoformat()
        }
    
    def _merge_results(self, main_results: Dict[str, List[str]], new_results: Dict[str, List[str]]):
        """合并仿真结果"""
        for category, files in new_results.items():
            if category in main_results:
                main_results[category].extend(files)
            else:
                main_results[category] = files.copy()
    
    def get_simulation_status(self) -> Dict[str, Any]:
        """获取仿真状态信息"""
        return {
            'config_summary': self.config_manager.to_dict(),
            'simulators': {
                'targets': len(self.target_simulators),
                'jammers': len(self.jammer_simulators),
                'recons': len(self.recon_simulators)
            },
            'threads': self.num_threads,
            'output_info': self.output_manager.get_session_info()
        }
